<?php if (! defined('BASEPATH')) exit('No direct script access allowed');

class Home extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->include_css('../frontend/css/home.css');
        $this->include_js('../frontend/js/home.js');
        $this->set_layout('frontend/layouts/default');
    }

    public function get_noticias()
    {
        $this->load->model('conteudo_model');

        $conteudos = $this->conteudo_model->get_conteudo_rest();
        $data = array('conteudos' => $conteudos);

        if (!empty($conteudos)) {
            return $this->load->view('frontend/home/<USER>', $data, TRUE);
        }

        return null;
    }

    public function index()
    {
        redirect('/frontend/enviar_arquivos');
        $user_lang = sess_user_language();

        $this->lang->load('home', $user_lang);
        $this->lang->load('login', $this->session->userdata("language"));
        // desativado
        // $this->load->library('api_bcx_noticias');
        // $this->api_bcx_noticias->get_post_list();

        $data = array();

        $this->set_header_info('Portal');

        $this->load->helper(array('text', 'produto_helper'));
        $this->load->model(
            array('empresa_model', 'produto_model')
        );
        // $data['usuario'] = $this->usuario_model->get_entry(sess_user_id());
        try {
            $empresa = $this->empresa_model->get_entry(sess_user_company());
        } catch (Exception $e) {
            $this->session->set_flashdata('error', $e->getMessage());
            redirect('login');
        }

        $data['empresa'] = $empresa;

        $this->load->model('grupo_model');

        try {
            $grupo = $this->grupo_model->get_entry(sess_user_grupo());
        } catch (Exception $e) {
            $grupo = array();
        }


        if (isset($grupo->logo_filename) and !empty($grupo->logo_filename)) {
            $data['logo_grupo'] = site_url(config_item('upload_logo_grupo_path') . $grupo->logo_filename);
        } else {
            $data['logo_grupo'] = NULL;
        }

        $data['produtos'] = array();

        $id_grupo = sess_user_grupo();
        $empresas = sess_user_companies();
        $data['noticias'] = $this->get_noticias();
        $data['outros_produtos'] = $this->produto_model->get_random_disabled_entry($id_grupo, $empresas);

        if (has_role('atalho_produtos')) {
            $data['produtos'] = $this->produto_model->get_active_products($id_grupo, $empresas, FALSE, FALSE);
        }

        $this->load->model('banner_model');

        $data['has_banner'] = FALSE;
        if ($data['banners'] = $this->banner_model->get_banner_em_vigencia()) {
            $data['has_banner'] = TRUE;
        }

        $data['language'] = $user_lang;

        $this->include_js('jquery.cookie.js');
        $this->render('frontend/home/<USER>', $data);
    }

    public function enviar_sugestao()
    {
        if ($post = $this->input->post()) {
            $this->load->model('usuario_model');
            $this->load->library('email');

            $usuario = $this->usuario_model->get_entry(sess_user_id());
            $sugestao = $post['sugestao'];

            $template['base_url'] = config_item('online_url');
            $template['html_message'] = '<h4>Nova sugestão enviada</h4>' .
                '<p>Uma nova sugestão foi enviada através do Portal de Clientes, verifique abaixo:</p>' .
                '<div style="border-left: 3px solid #ccc; background: #f2f2f2;margin-top: 15px;padding: 20px;">' .
                '    <p><strong>Usuário: </strong>' . $usuario->nome . '</p>' .
                '    <p><strong>E-mail: </strong>' . $usuario->email . '</p>' .
                '    <p><strong>Sugestão: </strong>' . $sugestao . '</p>' .
                '    <p><strong>Data: </strong>' . date("d/m/Y") . '</p>' .
                '</div>';

            $body = $this->load->view('templates/basic_template', $template, TRUE);

            $this->email->to(config_item('mail_suggestion_to'));
            $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
            $this->email->subject('[Portal DWTAX] - Nova sugestão');
            $this->email->message($body);

            if ($this->email->send()) {
                $this->message_next_render('<strong>OK! </strong> Sua sugestão foi encaminhada com sucesso.', 'success');
            } else {
                $this->message_next_render('<strong>Oops! </strong> Ocorreu um erro, tente novamente mais tarde ou contate um administrador.', 'error');
            }

            // print_r(); die();
            redirect($post['url']);
        }
    }
}
