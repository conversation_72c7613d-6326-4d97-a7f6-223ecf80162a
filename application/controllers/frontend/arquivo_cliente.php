<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Arquivo_cliente extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model(array(
            'arquivo_cliente_model',
            'usuario_model',
            'grupo_model'
        ));

        $this->set_layout('frontend/layouts/default');

        $this->load->library('breadcrumbs');
        $this->breadcrumbs->push('<span class="glyphicon glyphicon-home"></span>', '/');
    }

    private function apply_filters_default($language = false)
    {
        $data = array();

        $data['nome_arquivo_zip'] = $this->input->get('nome_arquivo_zip');
        if (!empty($data['nome_arquivo_zip'])) {
            $this->arquivo_cliente_model->set_state('nome_arquivo_zip', $data['nome_arquivo_zip']);
        }
        if (!$this->input->is_set('nome_arquivo_zip')) {
            $data['nome_arquivo_zip'] = "";
        }

        $data['nome_arquivo'] = $this->input->get('nome_arquivo');
        if (!empty($data['nome_arquivo'])) {
            $this->arquivo_cliente_model->set_state('nome_arquivo', $data['nome_arquivo']);
        }
        if (!$this->input->is_set('nome_arquivo')) {
            $data['nome_arquivo'] = "";
        }

        if ($language) {
            $this->arquivo_cliente_model->set_state("language", $language);
        }

        $data['data_envio_ini'] = $this->input->get('data_envio_ini');
        if (!empty($data['data_envio_ini'])) {
            $this->arquivo_cliente_model->set_state('data_envio_ini', $data['data_envio_ini']);
        }

        $data['data_envio_fim'] = $this->input->get('data_envio_fim');
        if (!empty($data['data_envio_fim'])) {
            $this->arquivo_cliente_model->set_state('data_envio_fim', $data['data_envio_fim']);
        }
    }

    public function index()
    {
        $user_lang = sess_user_language();

        $this->lang->load('arquivos_recebidos', $user_lang);

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('baixar_arquivos'), '/frontend/arquivo_cliente');

        $this->load->library('pagination');

        $page = $this->input->get('per_page');
        $limit = 10;
        $offset = ($page > 0 ? $page - 1 : 0) * $limit;

        $data = array();
        $data['data_envio_ini']  = $this->input->get('data_envio_ini');
        $data['data_envio_fim']  = $this->input->get('data_envio_fim');
        $data['search']          = $this->input->get('search');
        $data['search_file']     = $this->input->get('search_file');

        $this->arquivo_cliente_model->clear_states();

        $this->apply_filters_default($user_lang);

        $arquivos = $this->arquivo_cliente_model->get_entries_for_cliente($limit, $offset);

        foreach ($arquivos as $arquivo) {
            $nome = 'Usuário removido';
            try {
                $nome = $this->usuario_model->get_entry($arquivo->id_usuario)->nome;
            } catch (\Exception $e) {
            }
            $arquivo->id_usuario = $nome;
            $lista_arquivos = $this->arquivo_cliente_model->get_entries_lista_arquivos($arquivo->nome_arquivo);

            $lista_arquivos_dentro_zip = [];
            for ($k = 0; $k < count($lista_arquivos); $k++) {
                $lista_arquivos_dentro_zip[$k] =
                    $lista_arquivos[$k]->nome_arquivo;
            }

            $arquivo->lista_arquivos_dentro_zip = $lista_arquivos_dentro_zip;
        }

        $data['arquivos'] = $arquivos;

        $this->include_js(array(
            '../frontend/js/sweetalert2.min.js'
        ));

        $this->include_css(array(
            '../frontend/css/sweetalert2.min.css',
        ));

        $total_entries = $this->arquivo_cliente_model->get_total_entries_for_cliente();

        $query_str = '';

        $config['first_link']        = $this->lang->line('pagination1');
        $config['last_link']         = $this->lang->line('pagination2');
        $config['next_link']         = $this->lang->line('pagination3');
        $config['prev_link']         = $this->lang->line('pagination4');
        $config['base_url'] = base_url("frontend/arquivo_cliente?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['total_entries'] = $total_entries;
        $data['pagination'] = $this->pagination->create_links();

        $data['language'] = $user_lang;

        $this->render('frontend/arquivo_cliente/default', $data);
    }

    public function download($nome_encriptado = '', $email = false)
    {
        if (empty($nome_encriptado)) {
            $id_arquivo = $this->input->get('id_arquivo');
            $arquivo_cliente = $this->arquivo_cliente_model->get_entry($id_arquivo);
            $nome_encriptado = $arquivo_cliente->nome_arquivo;
        }

        $this->validation_download($nome_encriptado, sess_user_id());

        $this->quantidade_downloads($nome_encriptado);

        $entry = $this->arquivo_cliente_model->get_entry_by_encrypted_name($nome_encriptado);

        $ip = $_SERVER['REMOTE_ADDR'];

        if ($email) {
            $this->log($entry, sess_user_id(), $ip);
        }

        $config_path = config_item('upload_path');
        $upload_path = $config_path . 'arquivo_cliente/';
        $file_path = $upload_path . $nome_encriptado;

        setcookie("fileLoading", 'downloading', time() + 3600, '/');

        header('Content-Description: File Transfer');
        header('Content-Disposition: attachment; filename="' . $entry->nome_arquivo . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file_path));

        readfile($file_path);

        $dados_cliente = $this->usuario_model->get_entry(sess_user_id());

        $this->send_mail(array(
            'cliente' => $dados_cliente,
            'arquivo' => $entry
        ));

        $this->message_next_render('Download realizado com sucesso! </br>Um e-mail contendo a senha para abertura do arquivo foi enviado. </br>Verifique sua caixa postal.');

        exit();
    }

    public function validation_download($nome_arquivo, $id_user)
    {
        $arquivo = $this->arquivo_cliente_model->get_entry_by_cliente_and_name($nome_arquivo, $id_user);

        if (!count($arquivo)) {
            $this->message_next_render('<strong>Oops!</strong> Este arquivo não está associado ao seu usuário ou ele não está mais disponível!', 'error');
            redirect('frontend/arquivo_cliente/');
        }

        if ($arquivo->data_expiracao < date('Y-m-d')) {
            $this->message_next_render('<strong>Oops!</strong> Este arquivo já expirou, ele não está mais disponível!', 'error');
            redirect('frontend/arquivo_cliente/');
        }

        if ($arquivo->quantidade_downloads == 0) {
            $this->message_next_render('<strong>Oops!</strong> A quantidade de downlods já foi excedida, o arquivo não está mais disponível!', 'error');
            redirect('frontend/arquivo_cliente/');
        }
    }

    public function send_mail($data)
    {
        $user_lang = sess_user_language();

        $this->lang->load('email_arquivo_senha', $user_lang);

        $template_email = $this->load->view(
            'templates/template_password_cliente',
            array(
                'nome_arquivo' => $data['arquivo']->nome_arquivo,
                'code' => base64_decode($data['arquivo']->password),
                'cliente' => $data['cliente']
            ),
            TRUE
        );

        $name = explode('.', $data['arquivo']->nome_arquivo);

        $this->load->library('email');

        $this->email->initialize(array(
            'mailtype' => 'text'
        ));

        $this->email->to($data['cliente']->email);
        $this->email->cc(config_item('mail_2fa_copy'));
        $this->email->from(config_item('mail_from_cpd_addr'));
        $this->email->subject($this->lang->line('assunto') . '[' . substr($name[0], 0, 10) . '...]');
        $this->email->message($template_email);

        $this->email->send(TRUE);

        $this->send_mail_consultor($data);
    }

    private function send_mail_consultor($data)
    {
        $arquivo = $data['arquivo'];
        $cliente = $data['cliente'];

        $consultor = $this->usuario_model->get_entry($arquivo->id_usuario);

        $qtd_downloads = config_item('quantidade_downloads_arquivo_cliente');

        $user_lang = sess_user_language($consultor->id_usuario);

        $this->lang->load('arquivos_cliente', $user_lang);

        $template_email = $this->load->view(
            "templates/template_download_arquivo_{$user_lang}",
            array(
                'to' => $cliente->nome,
                'from' => $consultor->nome,
                'to_email' => $cliente->email,
                'from_email' => $consultor->email,
                'arquivo' => $arquivo,
                'downloads_restantes' => $qtd_downloads - ($qtd_downloads - $arquivo->quantidade_downloads),
                'arquivos_zip' => $this->arquivo_cliente_model->get_entries_lista_arquivos($arquivo->nome_arquivo)
            ),
            TRUE
        );

        $this->load->library('email');

        $this->email->initialize(array(
            'mailtype' => 'text'
        ));

        $this->email->to($consultor->email);
        $this->email->from(config_item('mail_from_cpd_addr'));
        //todo
        $this->email->subject("[Portal DWTAX] - " . $this->lang->line('send_mail_consultor_title'));
        $this->email->message($template_email);

        $this->email->send(TRUE);
    }

    public function quantidade_downloads($nome_arquivo)
    {
        $arquivo = $this->arquivo_cliente_model->get_entry_by_cliente_and_name($nome_arquivo, sess_user_id());

        $doc_data = array(
            'nome_arquivo' => $arquivo->nome_arquivo,
            'password' => $arquivo->password,
            'id_usuario' => $arquivo->id_usuario,
            'id_cliente' => $arquivo->id_cliente,
            'status' => $arquivo->status,
            'data_expiracao' => $arquivo->data_expiracao,
            'criado_em' => $arquivo->criado_em,
            'quantidade_downloads' => $arquivo->quantidade_downloads - 1
        );

        $this->arquivo_cliente_model->save($doc_data, array('id_arquivo' => $arquivo->id_arquivo));
    }

    public function excluir()
    {
        $user_id   = sess_user_id();
        $user_lang = sess_user_language($user_id);
        $this->lang->load('arquivos_cliente', $user_lang);

        $data['success'] = false;
        $id_list = $this->input->post('id_list');

        foreach ($id_list as $id_arquivo) {
            $arquivo = $this->arquivo_cliente_model->get_entry($id_arquivo);

            if (empty($arquivo)) continue; // If is not valid, skip it

            if ($this->validationExcluir($arquivo->nome_arquivo)) {
                $this->arquivo_cliente_model->update_status_cliente($arquivo->nome_arquivo, $user_id);

                $config_path = config_item('upload_path');
                $upload_path = $config_path . 'arquivo_cliente/';

                if (file_exists($upload_path . $arquivo->nome_arquivo)) {
                    unlink($upload_path . $arquivo->nome_arquivo); // Unlink a file
                } else {
                    $this->message_next_render($this->lang->line('erro_excluir'), 'error');
                }

                $this->log($arquivo, $user_id);

                $this->message_next_render($this->lang->line('sucesso_excluir'));
                $data['success'] = true;
            }
        }

        echo json_encode($data);
    }

    private function validationExcluir($nome_arquivo)
    {
        $validation = $this->arquivo_cliente_model->get_entries_for_cliente_by_name($nome_arquivo);

        if (count($validation) > 1) {
            $this->message_next_render('O arquivo "' . $nome_arquivo . '", está sendo compartilhado com outros clientes. <br>
            Você não pode excluí-lo!', 'error');

            return false;
        } else {
            foreach ($validation as $arquivo) {
                if ($arquivo->quantidade_downloads == config_item('quantidade_downloads_arquivo_cliente')) {
                    $this->message_next_render('O arquivo ainda não foi baixado nenhuma vez!', 'error');

                    return false;
                }
            }
        }
        return true;
    }

    public function log($arquivo, $user, $ip = null)
    {
        if ($ip) {
            $action = 'download por link do email';
        } else {
            $action = 'excluir';
        }

        $data = array(
            'id_usuario' => $user,
            'nome_arquivo' => $arquivo->nome_arquivo,
            'action' => $action,
            'ip' => $ip,
            'criado_em' => date('Y-m-d H:i:s')
        );

        $this->arquivo_cliente_model->save_log_arquivo_cliente($data);
    }

    private function apply_filters_default_arquivos_cliente()
    {
        $data = array();

        $data['data_expiracao_ini'] = $this->input->get('data_expiracao_ini');
        if (!empty($data['data_expiracao_ini'])) {
            $this->arquivo_cliente_model->set_state('data_expiracao_ini', $data['data_expiracao_ini']);
        }

        $data['data_expiracao_fim'] = $this->input->get('data_expiracao_fim');
        if (!empty($data['data_expiracao_fim'])) {
            $this->arquivo_cliente_model->set_state('data_expiracao_fim', $data['data_expiracao_fim']);
        }

        $data['data_envio_ini'] = $this->input->get('data_envio_ini');
        if (!empty($data['data_envio_ini'])) {
            $this->arquivo_cliente_model->set_state('data_envio_ini', $data['data_envio_ini']);
        }

        $data['data_envio_fim'] = $this->input->get('data_envio_fim');
        if (!empty($data['data_envio_fim'])) {
            $this->arquivo_cliente_model->set_state('data_envio_fim', $data['data_envio_fim']);
        }

        $data['nome_arquivo_zip'] = $this->input->get('nome_arquivo_zip');
        if (!empty($data['nome_arquivo_zip'])) {
            $this->arquivo_cliente_model->set_state('nome_arquivo_zip', $data['nome_arquivo_zip']);
        }
        if (!$this->input->is_set('nome_arquivo_zip')) {
            $data['nome_arquivo_zip'] = "";
        }

        $data['nome_arquivo'] = $this->input->get('nome_arquivo');
        if (!empty($data['nome_arquivo'])) {
            $this->arquivo_cliente_model->set_state('nome_arquivo', $data['nome_arquivo']);
        }
        if (!$this->input->is_set('nome_arquivo')) {
            $data['nome_arquivo'] = "";
        }

        $data['id_grupo'] = $this->input->get('id_grupo');
        if (!empty($data['id_grupo'])) {
            $this->arquivo_cliente_model->set_state('id_grupo', $data['id_grupo']);
        }
        if (!$this->input->is_set('id_grupo')) {
            $data['id_grupo'] = array();
        }

        $data['id_cliente'] = $this->input->get('id_cliente');
        if (!empty($data['id_cliente'])) {
            $this->arquivo_cliente_model->set_state('id_cliente', $data['id_cliente']);
        }

        $data['id_usuario'] = $this->input->get('id_usuario');
        if (!empty($data['id_usuario'])) {
            $this->arquivo_cliente_model->set_state('id_usuario', $data['id_usuario']);
        }
        if (!$this->input->is_set('id_usuario')) {
            $data['id_usuario'] = array();
        }

        return $data;
    }

    public function arquivos_cliente()
    {
        $user_lang = sess_user_language();

        $this->lang->load('arquivos_cliente', $user_lang);

        $this->arquivo_cliente_model->clear_states();

        $this->load->model([
            'usuario_model',
            'grupo_model'
        ]);

        $this->load->library('pagination');

        $data       = $this->apply_filters_default_arquivos_cliente();
        $http_query = http_build_query($data);

        $page   = $this->input->get('per_page');
        $limit  = 10;
        $offset = ($page > 0 ? $page - 1 : 0) * $limit;

        $arquivos      = $this->arquivo_cliente_model->get_entries($limit, $offset, sess_user_id());
        $total_entries = $this->arquivo_cliente_model->get_total_entries(sess_user_id());

        foreach ($arquivos as $index => $arquivo) {
            // Nome dos arquivos internos
            $nome_arquivos_internos = array_map(function($arquivo_interno) {
                return $arquivo_interno->nome_arquivo;
            }, $this->arquivo_cliente_model->get_entries_lista_arquivos($arquivo->nome_arquivo));

            // Relação com o cliente
            $arquivos_relacionados = $this->arquivo_cliente_model->get_entries_by_encrypted_name($arquivo->nome_arquivo, sess_user_id());

            // O cliente
            $clientes = array_map(function($arquivo_relacionado) {
                return [
                    "nome"                 => $arquivo_relacionado->nome_cliente,
                    "quantidade_downloads" => $arquivo_relacionado->quantidade_downloads
                ];
            }, $arquivos_relacionados);

            // O grupo
            $grupo = $this->grupo_model->get_entry_by_user($arquivo->id_usuario);

            // Concatena os valores em um item para listagem
            $data['list'][$index] = [
                'status'                 => $arquivo->status,
                'id_arquivo'             => $arquivo->id_arquivo,
                'id_usuario'             => $arquivo->id_usuario,
                'nome_arquivo'           => $arquivo->nome_arquivo,
                'clientes'               => $clientes,
                'nome_usuario'           => $arquivo->nome_usuario,
                'nome_arquivos_internos' => $nome_arquivos_internos,
                'data_expiracao'         => date('d/m/Y', strtotime($arquivo->data_expiracao)),
                'criado_em'              => date('d/m/Y H:i:s', strtotime($arquivo->criado_em)),
                'nome_grupo'             => $grupo->nome
            ];
        }

        $config['first_link']        = $this->lang->line('pagination1');
        $config['last_link']         = $this->lang->line('pagination2');
        $config['next_link']         = $this->lang->line('pagination3');
        $config['prev_link']         = $this->lang->line('pagination4');
        $config['base_url']          = base_url("frontend/arquivo_cliente/arquivos_cliente?". $http_query);
        $config['use_page_numbers']  = TRUE;
        $config['total_rows']        = $total_entries;
        $config['per_page']          = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['total_entries'] = $total_entries;
        $data['pagination']    = $this->pagination->create_links();
        $data['usuarios']      = $this->usuario_model->get_entries_becomex_sent_file();
        $data['grupos']        = $this->grupo_model->get_grupo_has_already_received_files();
        $data['language']      = $user_lang;

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('todos_arquivos'), '/frontend/arquivo_cliente/arquivos_cliente');

        $this->set_header_info($this->lang->line('header_info'));
        $this->set_title_page($this->lang->line('title_page'));

        $this->include_js(array(
            '../frontend/js/sweetalert2.min.js',
            'bootstrap-select/bootstrap-select.js'
        ));

        $this->include_css(array(
            '../frontend/css/sweetalert2.min.css',
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->render('frontend/arquivo_cliente/default_arquivos_clientes', $data);
    }

    public function novo_arquivo_cliente()
    {
        $user_lang = sess_user_language();

        $this->lang->load('novos_arquivos_cliente', $user_lang);

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('todos_arquivos'), '/frontend/arquivo_cliente/arquivos_cliente');
        $this->breadcrumbs->push($this->lang->line('novo_arquivo'), '/frontend/arquivo_cliente/novo_arquivo_cliente');

        $data = array();

        if ($post = $this->input->post()) {
            if (empty($post['id_cliente'])) {
                $this->message_on_render($this->lang->line('erro'), 'error');
            }

            $data = array(
                'data_expiracao' => date_format(date_create(str_replace('/', '-', $post['data_expiracao'])), 'Y/m/d'),
                'id_cliente'     => explode(',', $post['id_cliente']),
                'text_email'     => $post['text_email']
            );

            // File process
            if (!empty($_FILES)) {
                $date      = date('Y/m/d H:i:s');
                $user      = $this->usuario_model->get_entry(sess_user_id());
                $grupo     = $this->grupo_model->get_entry($post['id_grupo']);

                $upload_path = config_item('upload_path');
                $target_path = $upload_path . 'arquivo_cliente/';

                if (!file_exists($target_path)) {
                    mkdir($target_path, 0777, TRUE);
                }

                $zip_name  = preg_replace('/[^a-zA-Z0-9_-]/s','_', $grupo->nome) . '-' . date('YmdHis');
                $file_name = base64_encode($date . sess_user_id());
                $dir       = $target_path . $file_name;
                $password  = $this->getNameRandom(20);

                if (!file_exists($dir)) {
                    mkdir($dir, 0777, TRUE);
                }

                // Zip compress
                if (!file_exists($dir) && !file_exists($target_path)) {
                    $this->message_on_render($this->lang->line('erro_upload'), 'error');
                }


                $file_names = [];
                foreach ($_FILES as $file) {
                    for ($i = 0; $i < count($file['name']); $i++) {
                        if (!empty($file['name'][$i])) {
                            move_uploaded_file($file['tmp_name'][$i], $dir . '/' . $file['name'][$i]);
                            array_push($file_names, $file["name"][$i]);
                        }
                    }
                }

                if (file_exists($dir) && file_exists($target_path)) {
                    // echo shell_exec("zip -P " . $password . " -r -j '" . $upload_path . $nameZip . ".zip' '" . $folder_aux . "'");
                    echo shell_exec("zip -P " . $password . " -r -j '" . $target_path . $zip_name . ".zip' '" . $dir . "'");
                } else {
                    $this->message_on_render($this->lang->line('erro_upload'), 'error');
                }

                $compressed_file_data = null;
                foreach ($data['id_cliente'] as $id_cliente) {
                    $file_data = array(
                        'nome_arquivo'         => $zip_name . '.zip',
                        'password'             => base64_encode($password),
                        'id_usuario'           => sess_user_id(),
                        'id_cliente'           => $id_cliente,
                        'status'               => TRUE,
                        'data_expiracao'       => $data['data_expiracao'],
                        'criado_em'            => date('Y-m-d H:i:s'),
                        'quantidade_downloads' => config_item('quantidade_downloads_arquivo_cliente'),
                    );
                    $compressed_file_data = $file_data;
                    $this->arquivo_cliente_model->save($file_data);
                }

                // Send mail
                foreach ($data['id_cliente'] as $id_cliente) {
                    $data_email = array(
                        'arquivos_nomes'     => $file_names,
                        'arquivo_comprimido' => $compressed_file_data,
                        'cliente'            => $this->usuario_model->get_entry($id_cliente),
                        'autor'              => sess_user_nome(),
                        'grupo'              => $grupo->nome,
                        'caixa'              => $post['caixa'],
                        'data_envio'         => $compressed_file_data['criado_em'],
                        'mensagem'           => $data['text_email'],
                        'admin'              => $user,
                        'base_url'           => config_item('online_url')
                    );
                    $this->send_mail_novo_arquivo_cliente($data_email);
                }

                foreach ($_FILES as $file) {
                    for ($i = 0; $i < count($file['name']); $i++) {
                        if (!empty($file['name'][$i])) {
                            $file_data = array(
                                'name_zip'     => $zip_name . '.zip',
                                'nome_arquivo' => $file['name'][$i]
                            );
                        }
                        $this->arquivo_cliente_model->save_lista_arquivos($file_data);
                    }
                }

                foreach ($_FILES as $file) {
                    for ($i = 0; $i < count($file['name']); $i++) {
                        if (!empty($file['name'][$i])) {
                            $final_path = $dir . '/' . $file['name'][$i];
                            if (!file_exists($final_path)) {
                                $this->message_on_render($this->lang->line('erro_upload'), 'error');
                            }
                            unlink($final_path);
                        }
                    }
                }

                if (file_exists($dir)) {
                    rmdir($dir);
                } else {
                    $this->message_on_render($this->lang->line('erro_upload'), 'error');
                }
            }

            $this->message_next_render($this->lang->line('sucesso_upload'));
            redirect('frontend/arquivo_cliente/arquivos_cliente');
        }

        $data['clientes'] = [];
        $data['grupos']   = $this->grupo_model->get_entries();
        $data['language'] = $user_lang;
        $data['data_expiracao'] = date('d/m/Y', strtotime('+'. config_item('dias_expiracao_download_arquivos') .' day'));

        $this->include_js(array(
            'ckeditor/ckeditor.js?v=1',
            'bootstrap-select/bootstrap-select.min.js',
            'select2.js',
            '../frontend/js/dropzone.js',
            'bootstrap-select/bootstrap-select.js',
            '../frontend/js/sweetalert2.min.js'
        ));

        $this->include_css(array(
            'select2.css',
            'select2-bootstrap.css',
            'bootstrap-select/bootstrap-select.min.css',
            '../frontend/css/dropzone.css',
            '../frontend/css/enviar_arquivos.css',
            '../frontend/css/sweetalert2.min.css',
            'bootstrap-select/bootstrap-select.css'
        ));

        $this->set_header_info($this->lang->line('header_page'));
        $this->set_title_page($this->lang->line('title_upload'));

        $this->render('frontend/arquivo_cliente/novo_arquivo_cliente', $data);
    }

    public function getNameRandom($n)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';

        for ($i = 0; $i < $n; $i++) {
            $index = rand(0, strlen($characters) - 1);
            $randomString .= $characters[$index];
        }

        return $randomString;
    }

    public function send_mail_novo_arquivo_cliente($data)
    {
        $this->lang->load('email_arquivos', $data['cliente']->language);

        $template_email = $this->load->view("templates/template_arquivos_cliente", $data, TRUE);

        $this->load->library('email');
        $config['mailtype'] = 'html';
        $this->email->initialize($config);

        $this->email->to($data['cliente']->email);
        $this->email->from(config_item('mail_from_cpd_addr'));
        $this->email->subject($this->lang->line('assunto'));
        $this->email->message($template_email);

        $this->email->send();
    }
}
