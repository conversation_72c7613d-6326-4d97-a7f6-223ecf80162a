<?php if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

/**
 * <PERSON><PERSON><PERSON> do upload da aplicação setados pelo código: 150MB
 * /application/controller/enviar_arquivos.php { Metodo construct: ini_set     }
 * /assets/frontend/js/enviar_arquivos.js      { Parametro       : maxFilesize }
 */

class Enviar_arquivos extends MY_Controller
{
    /**
     * Metodo construtor
     * @method  __construct
     * @return  void  Sem retornos
     */
    public function __construct()
    {
        parent::__construct();

        // Auxílio ao dropzone
        set_time_limit(0);

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model(array(
            'usuario_model',
            'empresa_model',
            'cpd/arquivo_model',
            'upload_config_model',
            'cpd/arquivo_log_model',
            'cpd/arquivo_grupo_model',
            'cpd/arquivo_rel_destinatario',
            'cpd/processamento_model',
            'termo_responsabilidade_model',
            'termo_responsabilidade_logs_model'
        ));

        $this->load->helper('arquivo_log');
        $this->load->helper('arquivo_log_helper');

        $this->set_layout('frontend/layouts/default');

        $this->load->library('breadcrumbs');
        $this->breadcrumbs->push('<span class="glyphicon glyphicon-home"></span>', '/');
    }

    /**
     * Metodo de inicialização
     * @method  index
     * @return  void  Sem retornos
     */
    public function home_index()
    {
        redirect('/frontend/enviar_arquivos');
    }

    public function index()
    {
        $data = array();

        $lang = sess_user_language();
        $data['language'] = $lang;
        $this->lang->load('enviar_arquivos', $lang);
        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');

        $options = [
            [
                'url' => 'frontend/enviar_arquivos/processamento',
                'title' => $this->lang->line('card1'),
                'icon' => 'file-for-becomex.svg',
                'permission' => 'enviar_arquivos_becomex',
                'show' => true
            ],
            // [
            //     'url' => 'frontend/enviar_arquivos/usuarios_x_clientes',
            //     'title' => $this->lang->line('card2'),
            //     'icon' => 'usuarios-clientes.svg',
            //     'permission' => 'usuarios_x_clientes',
            //     'show' => (sess_user_tipo() == 'colaborador')
            // ],
            [
                'url' => 'frontend/enviar_arquivos/monitoramento',
                'title' => $this->lang->line('card3'),
                'icon' => 'search-files.svg',
                'permission' => 'acompanhar_envios_becomex',
                'show' => true
            ],
            // [
            //     'url' => 'frontend/arquivo_cliente/arquivos_cliente',
            //     'title' => $this->lang->line('card4'),
            //     'icon' => 'files-for-users.svg',
            //     'permission' => 'enviar_arquivos_clientes',
            //     'show' => (sess_user_tipo() == 'colaborador')
            // ],
            [
                'url' => 'frontend/arquivo_cliente',
                'title' => $this->lang->line('card5'),
                'icon' => 'becomex-file-for-user.svg',
                'permission' => 'receber_arquivos_becomex',
                'show' => (sess_user_tipo() == 'cliente')
            ],
        ];

        $data['options'] = [];

        foreach ($options as $option) {
            if (has_role($option['permission']) && !empty($option['show'])) {
                $data['options'][] = $option;
            }
        }

        return $this->render('frontend/enviar_arquivos/default', $data);
    }
    public function get_logs($log_local = FALSE)
    {
        $this->load->helper('arquivo_log_helper');

        $id = $this->input->post('arquivo');
        $logs =  $this->arquivo_model->get_logs_unique($id, $log_local);
        $log_data = [];

         foreach ($logs as $i => $log)
         {
            if (empty($log->extra))
            {
                $c = get_status_log_data($log, true);
                $log_data[] = '<div class="steps-step" style="width: 150px; min-width: 150px">'.
                '<a type="button" disabled="true" class="btn btn-default btn-circle">'.
                '<i class="'. $c->icon .'"></i>'.
                '</a><p>'.$c->title.'<br>'.date('H:i:s', strtotime($log->criado_em)).'</p> </div>';
            }
         }
         $objArray = array();

         foreach ($log_data as $value) {
             $obj = new stdClass();
             $obj->html = $value;
             $objArray[] = $obj;
         }
         echo json_encode($objArray);
         return;
    }

    /**
     * Metodo para seleção de pendencias de um grupo
     * @method  selecionar_pendencias_grupo
     * @param   object  $usuario  Usuario de um grupo
     * @return  void              Sem retornos
     */
    private function selecionar_pendencias_grupo($usuario)
    {
        $id_usuario = $usuario->id_usuario;
        $id_grupo = sess_user_grupo();
        $cnpj = sess_user_companies();

        $user_lang = sess_user_language();

        $this->lang->load('selecionar_pendencias', $user_lang);

        $id_produtos = $this->produto_model->get_active_products($id_grupo, $cnpj, TRUE);

        $limit = 30;

        if (!empty($id_produtos)) {
            $data['pendencias'] = $this->pendencia_model->get_entries_from_user($id_usuario, $id_grupo, $id_produtos);

            $this->pendencia_model->set_state('filter.enviado', 1);
            $this->pendencia_model->set_state('filter.order_by', 'pen.enviado_em');
            $data['pendencias_entregues'] = $this->pendencia_model->get_entries_from_user($id_usuario, $id_grupo, $id_produtos, $limit);

            $next_pendencias = $this->pendencia_model->get_entries_from_user($id_usuario, $id_grupo, $id_produtos, 1, ($limit * 1));
        } else {
            $data['pendencias'] = array();
            $data['pendencias_entregues'] = array();
            $next_pendencias = 0;
        }

        if (!empty($next_pendencias)) {
            $data['next_page'] = TRUE;
        }

        $data['language'] = $user_lang;

        $this->render('frontend/enviar_arquivos/selecionar_pendencias', $data);
    }

    /**
     * Metodo para seleção de pendencias de um prospect
     * @method  selecionar_pendencias_prospect
     * @param   object  $usuario  Prospect
     * @return  void              Sem retornos
     */
    private function selecionar_pendencias_prospect($usuario)
    {
        $id_usuario = $usuario->id_usuario;
        $cnpj = sess_user_companies();

        $user_lang = sess_user_language();

        $this->lang->load('selecionar_pendencias', $user_lang);

        $this->produto_model->set_state('filter.cnpj', $cnpj);
        $this->produto_model->set_state('filter.id_usuario', $id_usuario);
        $id_produtos = $this->produto_model->get_produtos_by_prospects($return_ids = true);

        if (!empty($id_produtos)) {

            $data['pendencias'] = $this->pendencia_model->get_entries_from_user_prospect($id_usuario, $cnpj, $id_produtos);

            $this->pendencia_model->set_state('filter.enviado', 1);
            $this->pendencia_model->set_state('filter.', 'pen.enviado_em');
            $data['pendencias_entregues'] = $this->pendencia_model->get_entries_from_user_prospect($id_usuario, $cnpj, $id_produtos, $limit);

            $next_pendencias = $this->pendencia_model->get_entries_from_user_prospect($id_usuario, $cnpj, $id_produtos, 1, ($limit * 1));
        } else {

            $data['pendencias'] = array();
            $data['pendencias_entregues'] = array();
            $next_pendencias = 0;
        }

        if (!empty($next_pendencias)) {
            $data['next_page'] = TRUE;
        }

        $data['language'] = $user_lang;

        $this->render('frontend/enviar_arquivos/selecionar_pendencias', $data);
    }

    /**
     * Metodo para seleção de pendencias
     * @method  selecionar_pendencias
     * @return  void  Sem retornos
     */
    public function selecionar_pendencias()
    {
        $user_lang = sess_user_language();

        $this->lang->load('selecionar_pendencias', $user_lang);

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('selecionar_pendencias'), '/frontend/enviar_arquivos/selecionar_pendencias');

        $this->load->helper('date_helper');

        $data = array();
        $limit = 10;

        $this->load->model('pendencia_model');

        $id_usuario = sess_user_id();
        $usuario = $this->usuario_model->get_entry($id_usuario);

        if ($usuario->tipo != 'prospect') {
            $this->selecionar_pendencias_grupo($usuario);
        } else {
            $this->selecionar_pendencias_prospect($usuario);
        }
    }


    /**
     * Metodo original para seleção de pendencias
     * @method  selecionar_pendencias_original
     * @return  void  Sem retornos
     */
    public function selecionar_pendencias_original()
    {
        $this->selecionar_pendencias();
    }


    /**
     * Metodo para a view de processamento, visão cliente
     * @method  processamento
     * @return  void  Sem retornos
     */
    public function processamento()
    {
        $user_lang = sess_user_language();

        $this->lang->load('processamento', $user_lang);

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');

        $data = array(
            'itens' => array()
        );

        if ($this->input->is_set('item')) {
            $this->breadcrumbs->push($this->lang->line('selecionar_pendencias'), '/frontend/enviar_arquivos/selecionar_pendencias');
            $itens = $this->input->get('item');

            foreach ($itens as $item) {
                $data['itens'][] = $this->pendencia_model->get_entry($item);
            }
        }

        $data['grupos'] = $this->grupo_model->get_entries();

        $data['cnpj'] = NULL;

        if (sess_user_tipo() != 'colaborador') {
            $empresa = $this->empresa_model->get_entry(sess_user_company());
            $data['cnpj'] = isset($empresa->cnpj) ? $empresa->cnpj : '';
        }

        $desabilitar_envio = false;

        if ($data['cnpj'] == CNPJ_BECOMEX && sess_user_tipo() != 'colaborador') {
            $desabilitar_envio = true;
        }

        $data['desabilitar_envio'] = $desabilitar_envio;

        if (empty($data['cnpj'])) {
            $grupo = sess_user_grupo();
            $data['cnpj'] = $this->xhr_get_cnpj_by_grupo($grupo, TRUE);
        }

        $termo = $this->termo_responsabilidade_model->get_entry($user_lang);
        $status = $this->termo_responsabilidade_logs_model->checkResponsabilitieTermStatus();

        $data['termo'] = $termo->text;
        $data['statusTermo'] = !empty($status);
        $data['destinatarios'] = $this->usuario_model->get_destinatarios_email();

        $data['upload_configs'] = array(
            'aduaneiro' => $this->upload_config_model->get_entry("aduaneiro-fiscal"),
            'bom' => $this->upload_config_model->get_entry("bom"),
            'outros' => $this->upload_config_model->get_entry("outros")
        );

        $data['language'] = $user_lang;

        $this->include_js(array(
            '../frontend/js/es6-promise.js',
            '../frontend/js/init-es6-promise.js',
            '../frontend/js/dropzone.js',
            '../frontend/js/enviar_arquivos.js',
            '../frontend/js/sweetalert2.min.js'
        ));

        $this->include_css(array(
            '../frontend/css/dropzone.css',
            '../frontend/css/enviar_arquivos.css',
            '../frontend/css/sweetalert2.min.css',
        ));

        $this->breadcrumbs->push($this->lang->line('envio_arquivos'), '/frontend/enviar_arquivos/processamento');

        $this->render('frontend/enviar_arquivos/processamento', $data);
    }

    /**
     * Metodo original para a view de processamento, visão cliente
     * @method  processamento_original
     * @todo          Não remova o método, nada a fazer
     * @return  void  Sem retornos
     */
    public function processamento_original()
    {
        $this->breadcrumbs->push('Central de Processamento e Download', '/frontend/enviar_arquivos');

        $data = array();
        $data['itens'] = array();

        if ($this->input->is_set('item')) {
            $this->breadcrumbs->push('Selecionar Pendências', '/frontend/enviar_arquivos/selecionar_pendencias');
            $itens = $this->input->get('item');

            foreach ($itens as $item) {
                $data['itens'][] = $this->pendencia_model->get_entry($item);
            }
        }

        $this->include_js(array(
            '../frontend/js/es6-promise.js',
            '../frontend/js/init-es6-promise.js',
            '../frontend/js/dropzone.js',
            '../frontend/js/enviar_arquivos_original.js',
            '../frontend/js/sweetalert2.min.js'
        ));

        $this->include_css(array(
            '../frontend/css/dropzone.css',
            '../frontend/css/enviar_arquivos.css',
            '../frontend/css/sweetalert2.min.css',
        ));

        $this->breadcrumbs->push('Envio de arquivos', '/frontend/enviar_arquivos/processamento_original');
        $this->render('frontend/enviar_arquivos_original/processamento', $data);
    }

    /**
     * Responsável por confirmar as pendências que foram resolvidas
     * @method  confirmacao
     * @return  void  Sem retornos
     */
    public function confirmacao()
    {
        if (!$this->input->is_ajax_request()) {
            $exit_message = utf8_decode('Método de requisição inválida, contate o administrador.'); // :'(
            die($exit_message);
        }
        $arquivos_sistemas = $this->input->post('arquivos_sistemas');
        $arquivos = $this->input->post('arquivos');

        if ($this->input->post('pendencias')) {
            $this->load->model('pendencia_model');

            $pendencias = $this->input->post('pendencias');
            $arr_pendencias = explode(',', $pendencias);

            foreach ($arr_pendencias as $id_pendencia) {
                $dbdata = array('enviado' => 1, 'enviado_em' => date('Y-m-d H:i:s'));
                $update_pendencia = $this->pendencia_model->save($dbdata, array('id_pendencia' => $id_pendencia));

                if ($update_pendencia === false) {
                    log_message('error', 'ERROR_CPD: Pendencia #' . $id_pendencia . ' não pode ser atualizada.');
                }
            }
        }

        if (!empty($arquivos)) {
            $this->send_admin_completed_notification($arquivos);
            $this->send_user_completed_notification($arquivos);
            if (!empty($arquivos_sistemas)) {
                $this->send_user_seguidor_completed_notification($arquivos_sistemas);
            }
        }
    }

    /**
     * Envia a mensagem de upload completo para o administrador
     * @method  send_admin_completed_notification
     * @param   array   $arquivos  Arquivos
     * @return  void               Sem retornos
     */
    private function send_admin_completed_notification($arquivos = array())
    {
        $this->load->model('usuario_model');

        $id_usuario_acao = sess_user_id();
        $usuario = $this->usuario_model->get_entry($id_usuario_acao);

        $pendencias = $this->input->post('pendencias');

        $message  = "<h4>Controle de Pendências</h4>";
        if (!empty($pendencias)) {
            $message .= "<p>Algumas pendências foram resolvidas pelo cliente <strong>{$usuario->nome}</strong> no Portal de Clientes, verifique abaixo as informações: </p>";
        } else {
            $message .= "<p>Alguns arquivos foram enviados de forma independente pelo cliente <strong>{$usuario->nome}</strong> no Portal de Clientes, verifique abaixo as informações: </p>";
        }
        $message .= "<div style='border-left: 4px solid #f2f2f2; padding: 15px; background: #f2f2f2;'>";
        $message .= "<p><strong>Arquivos aceitos/enviados: </strong></p>";

        $message_data = $this->get_completed_notification_message_body($arquivos, TRUE, TRUE);

        $message .= $message_data['body'];
        $data_email = array(
            'subject' => $message_data['subject'],
            'html_message' => $message,
            'to' => $this->config->item('mail_cpd_addr'),
            'base_url' => $this->config->item('online_url')
        );

        $this->send_mail_notification($data_email);
    }


    /**
     * Envia a mensagem de upload falho
     * @method  send_mail_upload_failed
     * @param   array   $subject  Assunto
     * @param   string  $message  Mensagem
     * @return  void              Sem retornos
     */
    private function send_mail_upload_failed($subject, $message, $destinatarios_copia = null)
    {
        $this->load->library('email');

        $body = $this->load->view('templates/basic_template', array(
            'html_message' => $message,
            'base_url' => $this->config->item('online_url')
        ), TRUE);

        $this->email->subject($subject);
        $this->email->to(sess_user_email());

        if (!empty($destinatarios_copia))
        {
            $this->email->cc($destinatarios_copia);
        }

        $this->email->from($this->config->item('mail_from_cpd_addr'));
        $this->email->message($body);

        if ($this->email->send() === false) {
            $nome_usuario = sess_user_nome();
            $message      = "Envio de e-mail realizado por: " . $nome_usuario . ".";
            log_message('error', 'Email_Queue_Error: ' . $message);
        }
    }

    /**
     * Envia a mensagem de upload completo
     * @method  send_user_completed_notification
     * @param   array  $arquivos  Os arquivos
     * @return  void              Sem retornos
     */
    private function send_user_completed_notification($arquivos = array())
    {
        $this->load->model('usuario_model');
        $usuario = $this->usuario_model->get_entry(sess_user_id());
        $pendencias = $this->input->post('pendencias');

        $user_lang = sess_user_language();
        $this->lang->load("email_controle_pendencias", $user_lang);

        $message  = $this->lang->line("message1");
        if (!empty($pendencias)) {
            $message .= "<p><strong>" . strtok($usuario->nome, " ") . $this->lang->line("message2");
            $message .= $this->lang->line("message3");
        } else {
            $message .= "<p><strong>" . strtok($usuario->nome, " ") . $this->lang->line("message4");
        }
        $message .= "<div style='border-left: 4px solid #f2f2f2; padding: 15px; background: #f2f2f2;'>";
        $message .= $this->lang->line("message5");

        $message_data = $this->get_completed_notification_message_body($arquivos, FALSE);

        $message .= $message_data['body'];
        $data_email = array(
            'subject' => $message_data['subject'],
            'html_message' => $message,
            'to' => $usuario->email,
            'base_url' => $this->config->item('online_url')
        );

        $this->send_mail_notification($data_email);
    }

    /**
     * Captura a mensagem da noficação quando completa
     * @method  get_completed_notification_message_body
     * @param   array  $arquivos            Os arquivos
     * @param   bool   $mostrar_pendencias  Se deve retornar as pendencias
     * @param   bool   $CPD                 Se CPD
     * @return  void                        Sem retornos
     * @return  bool                        Se não for possivel capturar o arquivo
     */
    private function get_completed_notification_message_body($arquivos = array(), $mostrar_pendencias = TRUE, $CPD = FALSE)
    {
        $this->load->model('pendencia_model');

        $user_lang = sess_user_language();
        $this->lang->load("email_controle_pendencias", $user_lang);

        $id_usuario = sess_user_id();
        $usuario = $this->usuario_model->get_entry($id_usuario);

        if ($usuario->tipo != 'prospect') {
            $this->load->model('grupo_model');

            try {

                $id_grupo_usuario_acao = sess_user_grupo();
                $grupo = $this->grupo_model->get_entry($id_grupo_usuario_acao);
            } catch (Exception $e) {

                $return = array(
                    'status'  => 'error',
                    'code'    => 500,
                    'message' => $e->getMessage() . $this->lang->line('texto1')
                );

                $this->output->set_content_type('application/json');
                $this->output->set_status_header($return['code']);
                $this->output->set_output(
                    json_encode($return)
                );

                return false;
            }

            $desc_subject_email = $grupo->nome;
        } else {

            // Limitado para pegar somente 1 raiz do usuário.
            $this->load->model('empresa_model');

            $id_prospect = sess_user_company();
            $prospect = $this->empresa_model->get_entry($id_prospect);

            $desc_subject_email = $prospect->razao_social;
        }

        $base_url = $this->config->item('online_url');

        $message = "<ul><li>" . implode("</li><li>", $arquivos) . "</li></ul>";

        $pendencias = $this->input->post('pendencias');

        $assunto1 = "";
        $assunto2 = "";

        if ($CPD) {
            $assunto1 = "Pendências resolvidas: ";
            $assunto2 = "Portal DWTAX: ";
        } else {
            $assunto1 = $this->lang->line('assunto1');
            $assunto2 = $this->lang->line('assunto2');
        }

        if (!empty($pendencias)) {
            $subject = character_limiter($assunto1 . $desc_subject_email, 50, '...');

            $arr_pendencias = explode(',', $pendencias);

            foreach ($arr_pendencias as $id_pendencia) {
                $pendencia = $this->pendencia_model->get_entry($id_pendencia);

                $message .= "<hr style='border: 0;border-bottom: 1px solid #ccc;'><strong>";
                if ($mostrar_pendencias) {
                    $message .= "<a href='{$base_url}cadastros/pendencia/editar/{$id_pendencia}'>". $this->lang->line('texto2') ." #{$id_pendencia}</a>";
                } else {
                    $message .= "<strong>". $this->lang->line('texto2') ." #{$id_pendencia}</strong>";
                }
                $message .= "</strong><br>";
                $message .= "<strong>". $this->lang->line('texto3') .":</strong> {$pendencia->descricao}<br>";
                $message .= "<strong>". $this->lang->line('texto4') .":</strong> {$pendencia->detalhe}<br>";
            }

            $message .= "</div><br><a href='{$base_url}cadastros/pendencia' target='blank'> ". $this->lang->line('texto5');
        } elseif (!empty($pendencias) && !$mostrar_pendencias) {
            $subject = character_limiter($assunto1 . $desc_subject_email, 50, '...');
            $message .= "</div>";
        } else {
            $subject = character_limiter($assunto2 . $desc_subject_email, 50, '...');
            $message .= "</div>";
        }

        return array(
            'subject' => $subject,
            'body' => $message
        );
    }

    /**
     * Executa o envio de email de notificação
     * @method  send_mail_notification
     * @param   array  $data_email  Os dados do email
     * @return  void                Sem retornos
     */
    private function send_mail_notification($data_email)
    {
        $this->load->library('email');

        $body = $this->load->view('templates/basic_template', $data_email, TRUE);

        $this->email->subject($data_email['subject']);
        $this->email->to($data_email['to']);
        $this->email->from($this->config->item('mail_from_cpd_addr'));
        $this->email->message($body);
        $s = $this->email->send();

        if ($s === false) {
            $nome_usuario = sess_user_nome();
            $message      = "Envio de e-mail realizado por: " . $nome_usuario . ".";
            log_message('error', 'Email_Queue_Error: ' . $message);
        }
    }

    /**
     * Verifica se existe a pasta para upload,
     * Se não existir, cria uma e adiciona um arquivo chamado index.html
     * @method  check_folder_upload
     * @param   string  $path       Caminho
     * @param   string  $base_path  Caminho base
     * @return  bool                Se encontrado
     */
    public function check_folder_upload($path = null, $base_path = null)
    {
        if ($path == null) {
            return false;
        }

        $this->load->helper('string');

        $concat_path = '';
        $folders     = explode('/', $path);

        foreach ($folders as $folder) {
            $concat_path .= '/' . $folder;

            $full_path = $base_path . $concat_path;
            $full_path = reduce_multiples($full_path, '/');

            if (!is_dir($full_path)) {
                mkdir($full_path, 0777);
                touch($full_path . '/' . "index.html");
            }
        }

        if (!is_dir($path)) {
            log_message('error', 'ERROR_CPD: A pasta de uploads não foi criada: ' . $path);
            return false;
        }

        return true;
    }

    /**
     * Requisição para próxima página
     * @method  ajax_get_next_page
     * @return  bool  Quando efetuado
     */
    public function ajax_get_next_page()
    {
        if ($this->input->post()) {
            $page = $this->input->post('page');

            $res = array();
            $id_usuario = sess_user_id();
            $limit = 10;
            $offset = (($page > 0 ? $page : 1) * $limit);
            $next_page_offset = (($page + 1) * $limit);

            $id_usuario = sess_user_id();
            $id_grupo = sess_user_grupo();
            $cnpj = sess_user_companies();

            $id_produtos = $this->produto_model->get_active_products($id_grupo, $cnpj, TRUE);

            if (!empty($id_produtos)) {
                $this->pendencia_model->set_state('filter.enviado', 1);

                $pendencias = $this->pendencia_model->get_entries_from_user($id_usuario, $id_grupo, $id_produtos, $limit, $offset);
                $next_pendencias = $this->pendencia_model->get_entries_from_user($id_usuario, $id_grupo, $id_produtos, 1, $next_page_offset);

                if (!empty($next_pendencias)) {
                    $res['next_page'] = TRUE;
                } else {
                    $res['next_page'] = FALSE;
                }

                $res['pendencias'] = $pendencias;
            } else {
                $res['next_page'] = FALSE;
                $res['pendencias'] = array();
            }

            echo json_encode($res);
            return TRUE;
        }
    }

    /**
     * Sessão de upload de arquivos
     */

    /**
     * Captura o nome do arquivo limpo
     * @method  get_nome_arquivo
     * @param   string  $nome_arquivo   Nome do arquivo
     * @return  string                  Nome do arquivo limpo
     */
    private function get_nome_arquivo($nome_arquivo)
    {
        $this->load->library('security');
        return $this->security->sanitize_filename($nome_arquivo);
    }

    /**
     * Captura o arquivo
     * @method  get_arquivo
     * @param   int     $unique_id      Identificador unico
     * @param   string  $nome_arquivo   Nome do arquivo
     * @param   string  $cnpj           CNPJ do usuario
     * @param   string  $tipo_arquivo   Tipo do arquivo
     * @param   string  $descricao      Descrição
     * @param   array   $args           Argumentos
     * @return  object                  Nome do arquivo limpo
     */
    private function get_arquivo($unique_id = NULL, $nome_arquivo = NULL, $cnpj = NULL, $tipo_arquivo = NULL, $descricao = NULL, $args = NULL)
    {
        $nome_arquivo = $this->get_nome_arquivo($nome_arquivo);

        $entry = $this->arquivo_model->get_entry($unique_id);
        if (empty($entry)) {
            $this->criar_processamento($unique_id, $nome_arquivo, $cnpj, $tipo_arquivo, $descricao, $args);

            $entry = $this->arquivo_model->get_entry($unique_id);
        }

        return $entry;
    }

    /**
     * Altera o dado de sessão para o id do grupo no arquivo
     * @method  xhr_grupo_finalizado
     * @return  void  Sem retornos
     */
    public function xhr_grupo_finalizado()
    {
        $this->session->set_userdata('id_grupo_arquivo', false);
    }

    /**
     * Altera o dado de sessão para o id do grupo no arquivo
     * @method  grupo_iniciado
     * @param   int   $id_grupo_arquivo  O id para o grupo a ser iniciado
     * @return  void                     Sem retornos
     */
    private function grupo_iniciado($id_grupo_arquivo)
    {
        $this->session->set_userdata('id_grupo_arquivo', $id_grupo_arquivo);
    }

    /**
     * Captura o arquivo correspondente ao id do grupo
     * @method  xhr_get_arquivo
     * @return  object  Arquivo por meio do id do grupo
     */
    private function xhr_get_arquivo()
    {
        $unique_id     = $this->input->post('id');
        $nome_arquivo  = $this->input->post('name');
        $cnpj          = $this->input->post('cnpj');
        $tipo_arquivo  = $this->input->post('tipo_arquivo');
        $descricao     = $this->input->post('descricao');
        $destinatarios = $this->input->post('destinatarios');

        $destinatarios = explode(';', $destinatarios);

        $destinatarios = $this->usuario_model->get_destinatarios_by_email(array_map(function($destinatario) {
            return trim($destinatario);
        }, $destinatarios));

        if (sess_user_tipo() == 'colaborador') {
            $id_grupo = $this->input->post('id_grupo');
        } else {
            $id_grupo = sess_user_grupo();
        }

        return $this->get_arquivo($unique_id, $nome_arquivo, $cnpj, $tipo_arquivo, $descricao, array(
            'id_grupo'      => $id_grupo,
            'destinatarios' => $destinatarios
        ));
    }

    /**
     * Log do upload
     * @method  xhr_log_iniciar_upload
     * @return  void  Sem retornos
     */
    public function xhr_log_iniciar_upload()
    {
        $entry = $this->xhr_get_arquivo();
        $this->log_upload($entry, Processamento_model::INICIADO);

        echo json_encode($entry); exit;
    }

    /**
     * Log do upload para o cancelamento
     * @method  xhr_log_cancelar_upload
     * @param   int   $id  O id do log
     * @return  bool       Se existir
     */
    public function xhr_log_cancelar_upload($id = null)
    {
        if (empty($id)) {
            return FALSE;
        }

        ini_set('memory_limit', '-1');

        $entry = $this->xhr_get_arquivo();

        $message  = "<h4>Olá, ".sess_user_nome()."!</h4>";
        $message .= "<p>O upload do arquivo <strong>$entry->nome_arquivo</strong> foi cancelado.</p>";

        $this->send_mail_upload_failed('[Beconnect] - Upload cancelado', $message);

        $this->alterar_status_processamento($entry->id_arquivo, Processamento_model::CANCELADO);

        $this->log_upload($entry, Processamento_model::CANCELADO);
    }

    /**
     * Log para o percentual
     * @method  xhr_log_percentual
     * @return  bool  Se existir
     */
    public function xhr_log_percentual()
    {
        $percentual = $this->input->post('percentual');

        $entry = $this->xhr_get_arquivo();

        $exists = $this->arquivo_log_model->check_log_percentual_exists($entry->id_arquivo, $percentual);

        if (!empty($exists)) return FALSE;

        $this->log_upload($entry, Processamento_model::PROGRESSO, $percentual);
    }

    /**
     * Captura o CNPJ por meio do id do grupo
     * @method  xhr_get_cnpj_by_grupo
     * @param   int     $id_grupo  O id do grupo
     * @param   bool    $return    Se deve haver retorno
     * @return  string  $cnpj      O CNPJ correspondente
     */
    public function xhr_get_cnpj_by_grupo($id_grupo = NULL, $return = FALSE)
    {
        try {
            $this->load->model('empresa_model');
            $entries = $this->empresa_model->get_entries_by_grupo($id_grupo, FALSE);

            $entries = format_simple_array($entries, 'cnpj');

            if (empty($entries) || !is_array($entries)) {
                throw new Exception('Nenhum valor encontrado');
            }

            sort($entries, SORT_NUMERIC);

            $cnpj = array_shift($entries);

            if ($return) return $cnpj;

            echo $cnpj;
        } catch (Exception $e) {
            if ($return) {
                return $e->getMessage() . ' ' . $id_grupo;
            }

            echo $e->getMessage();
            echo $id_grupo;
        }
    }

    /**
     * Realiza o log de um upload
     * @method  log_upload
     * @param   object  $entry  O arquivo
     * @param   string  $tipo   O evento para o log
     * @param   string  $extra  Informações extras
     * @return  object          O log do arquivo
     */
    private function log_upload($entry, $tipo, $extra = '')
    {
        if (empty($entry) && empty($tipo)) return FALSE;

        $dbdata = array(
            'id_arquivo'   => $entry->id_arquivo,
            'id_usuario'   => sess_user_id(),
            'cnpj_cliente' => $entry->cnpj_cliente,
            'evento'       => $tipo,
            'nome_arquivo' => $entry->nome_arquivo,
            'extra'        => $extra,
            'criado_em'    => date('Y-m-d H:i:s', time())
        );
        return $this->arquivo_log_model->save($dbdata);
    }

    /**
     * Verifica se o arquivo existe
     * @method  get_extension
     * @param   string  $file       O nome do arquivo
     * @return  bool                Se a extensão não existir
     * @return  string  $extension  Se a extensão existir
     */
    private function get_extension($file)
    {
        $extension = end(explode(".", $file));
        return $extension ? $extension : false;
    }

    /**
     * Verifica se o arquivo existe
     * @method  check_file_exists
     * @param   object  $entry         O arquivo
     * @param   int     $count         O número de arquivos
     * @param   string  $nome_arquivo  O nome do arquivo
     * @return  string  $nome_arquivo  O nome do arquivo correspondente
     */
    private function check_file_exists($entry, $count = 0, $nome_arquivo)
    {
        $entry = (object) $entry;
        $upload_path_root = getcwd() . DIRECTORY_SEPARATOR . config_item('upload_arquivos_cpd_path');
        $upload_path = $upload_path_root . DIRECTORY_SEPARATOR . $entry->cnpj_cliente . DIRECTORY_SEPARATOR . $nome_arquivo;

        if (file_exists($upload_path)) {
            $count++;
            $extension = $this->get_extension($entry->nome_arquivo);
            $nome_arquivo_orig = str_replace(".{$extension}", '', $entry->nome_arquivo);
            $nome_arquivo_orig = "{$nome_arquivo_orig}_{$count}.{$extension}";
            return $this->check_file_exists($entry, $count, $nome_arquivo_orig);
        }

        return $nome_arquivo;
    }

    /**
     * Cria um processo para o arquivo
     * @method  criar_processamento
     * @param   int     $unique_id     O id unico para o processamento
     * @param   string  $nome_arquivo  O nome do arquivo
     * @param   string  $cnp           O CNPJ responsavel
     * @param   string  $tipo_arquivo  O tipo de arquivo
     * @param   string  $descricao     A descrição
     * @param   array   $args          Argumentos a mais
     * @return  void                   Sem retornos
     */
    private function criar_processamento($unique_id = NULL, $nome_arquivo = NULL, $cnpj = NULL, $tipo_arquivo = NULL, $descricao = NULL, $args = NULL)
    {
        if (empty($nome_arquivo)) {
            throw new Exception('Tente novamente');
        }

        $id_grupo_arquivo = NULL;

        if (empty($this->session->userdata('id_grupo_arquivo'))) {
            $id_grupo_arquivo = $this->arquivo_grupo_model->save(array(
                'descricao' => uniqid(),
                'created_at' => date('Y-m-d H:i:s'),
                'id_grupo' => !empty($args['id_grupo']) ? $args['id_grupo'] : sess_user_grupo(),
                'id_usuario' => sess_user_id()
            ));

            $this->grupo_iniciado($id_grupo_arquivo);
        } else {
            $id_grupo_arquivo = $this->session->userdata('id_grupo_arquivo');
        }

        $dbdata = array(
            'id_arquivo'   => $unique_id,
            'id_usuario'   => sess_user_id(),
            'cnpj_cliente' => $cnpj,
            'status_processamento' => Processamento_model::ENTREGUE,
            'nome_arquivo' => $nome_arquivo,
            'criado_em'    => date('Y-m-d H:i:s', time()),
            'tipo_arquivo' => $tipo_arquivo,
            'descricao'    => $descricao,
            'id_grupo_arquivo' => $id_grupo_arquivo,
            'id_grupo' => isset($args['id_grupo']) && !empty($args['id_grupo']) ? $args['id_grupo'] : 0,
            'email_grupo_arquivo' => isset($args['destinatarios']) && !empty($args['destinatarios']) && count($args['destinatarios']) > 0 ? 0 : 1
        );

        $dbdata['nome_arquivo'] = $this->check_file_exists($dbdata, 0, $nome_arquivo);

        $this->arquivo_model->save($dbdata);

        if (!empty($args['destinatarios'])) {
            foreach ($args['destinatarios'] as $destinatario) {
                $dbdata = array(
                    'id_usuario' => $destinatario,
                    'id_arquivo' => $unique_id,
                    'id_grupo_arquivo' => $id_grupo_arquivo,
                    'email_enviado' => 0
                );

                $this->arquivo_rel_destinatario->save($dbdata);
            }
        }

    }

    /**
     * Realiza o upload de arquivos
     * @method  upload_file
     * @param   object  $entry  O arquivo para upload
     * @return  object          Os dados referentes ao arquivo
     */
    public function upload_file($entry = NULL)
    {
        print_r($entry);
exit;
        if (empty($_FILES['fileData']))
            throw new Exception('Nenhum arquivo recebido');

        $_FILES['fileData']['name'] = $entry->nome_arquivo;

        $upload_path_root = getcwd() . DIRECTORY_SEPARATOR . config_item('upload_arquivos_cpd_path');
        $upload_path = $upload_path_root . DIRECTORY_SEPARATOR . $entry->cnpj_cliente . DIRECTORY_SEPARATOR;

        if (!file_exists($upload_path_root)) {
            mkdir($upload_path_root, 0777);
            chmod($upload_path_root, 0777);
        }
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0777);
            chmod($upload_path, 0777);
        }

        $config['upload_path']   = $upload_path;
        $config['allowed_types'] = '*';
        $config['overwrite']     = TRUE;
        $config['remove_spaces'] = FALSE;

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('fileData'))
            throw new Exception('<h4>Ops... Ocorreu um erro</h4> ' . $this->upload->display_errors('<li>', '</li>'));

        return $this->upload->data();
    }

    /**
     * Realiza o envio de arquivos
     * @method  alterar_status_processamento
     * @param   int     $id      O id do arquivo
     * @param   string  $status  O status do arquivo
     * @return  void             Sem retornos
     */
    public function alterar_status_processamento($id = NULL, $status = '')
    {
        $this->arquivo_model->save(array(
            'status_processamento' => $status
        ), array(
            'id_arquivo' => $id
        ));
    }

    /**
     * Realiza o envio de arquivos
     * @method  enviar
     * @return  void  Sem retornos
     */
    public function enviar()
    {
        $nome_arquivo  = $_FILES['fileData']['name'];
        $unique_id     = $this->input->post('unique_id');
        $cnpj          = $this->input->post('cnpj');
        $tipo_arquivo  = $this->input->post('tipo_arquivo');
        $descricao     = $this->input->post('descricao');
        $destinatarios = $this->input->post('destinatarios');

        $destinatarios = explode(';', $destinatarios);

        $destinatarios = $this->usuario_model->get_destinatarios_by_email(array_map(function($destinatario) {
            return trim($destinatario);
        }, $destinatarios));

        if (sess_user_tipo() == 'colaborador') {
            $id_grupo = $this->input->post('id_grupo');
        } else {
            $id_grupo = sess_user_grupo();
        }

        $entry = $this->get_arquivo($unique_id, $nome_arquivo, $cnpj, $tipo_arquivo, $descricao, array(
            'id_grupo'      => $id_grupo,
            'destinatarios' => $destinatarios
        ));

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        try {
            if ($empresa->cnpj == CNPJ_BECOMEX && $cnpj == CNPJ_BECOMEX && sess_user_tipo() == 'colaborador') {
                throw new Exception('Não é possível enviar arquivos para a Empresa Becomex');
            }

            $data = $this->upload_file($entry);

            if (!empty($data)) {
                if (
                    isset($data['full_path'])
                    && file_exists($data['full_path'])
                    && !empty($data['full_path'])
                    && ENVIRONMENT == "production"
                ) {
                    chown($data['full_path'], 'portalclientes');
                }

                $this->alterar_status_processamento($entry->id_arquivo, Processamento_model::ENTREGUE);
                $this->log_upload($entry, Processamento_model::CONCLUIDO);
              //  $this->log_upload($entry, Processamento_model::AGUARDANDO);
                //$this->send_email_seguidores($entry);
            }

        } catch (Exception $e) {

            $destinatarios_email = [];
            if (is_array($destinatarios) && !empty($destinatarios))
            {
                foreach ($destinatarios as $destinatario_id)
                {
                    $usuario = $this->usuario_model->get_entry($destinatario_id);
                    $destinatarios_email[] = $usuario->email;
                }
            }

            $message  = "<h4>Olá, ".sess_user_nome()."!</h4>";
            $message .= "<p>Não foi possível realizar o upload do arquivo $nome_arquivo. Verifique se os padrões de envio foram seguidos.</p>";
            $message .= "<p>{$e->getMessage()}</p>";
            $message .= "<div style='border-left: 4px solid #f2f2f2; padding: 15px; background: #f2f2f2;'>";
            $message .= "<p><strong>Caso o erro persista, entre em contato! </strong></p>";

            $this->send_mail_upload_failed("[Beconnect] - Falha no upload", $message,$destinatarios_email);

            $this->log_upload($entry, Processamento_model::UPLOAD_FALHA, $e->getMessage());
        }
    }

    /**
     * Aplica os filtros padrões de consulta para o model de arquivos
     * @method  apply_filters_default
     * @param   bool  $language     Adiciona a linguagem para consulta (Opcional)
     * @return  void                Sem retornos
     */
    private function apply_filters_default($language = FALSE)
    {
        $data = array();

        if ($language) {
            $this->arquivo_model->set_state("language", $language);
        }

        $data['cnpj'] = $this->input->get('cnpj') ? $this->input->get('cnpj') : NULL;
        if (!empty($data['cnpj'])) {
            $this->arquivo_model->set_state('cnpj', $data['cnpj']);
        }

        $data['status'] = $this->input->get('status') ? $this->input->get('status') : NULL;
        if (!empty($data['status'])) {
            $this->arquivo_model->set_state('status_processamento_cliente_cpd', $data['status']);
        }

        $data['data_inicio'] = $this->input->get('data_inicio') ? $this->input->get('data_inicio') : NULL;
        if (!empty($data['data_inicio'])) {
            $this->arquivo_model->set_state('data_inicio', $data['data_inicio']);
        }

        $data['data_fim'] = $this->input->get('data_fim') ? $this->input->get('data_fim') : NULL;
        if (!empty($data['data_fim'])) {
            $this->arquivo_model->set_state('data_fim', $data['data_fim']);
        }

        $data['id_grupo'] = $this->input->get('id_grupo') ? $this->input->get('id_grupo') : NULL;
        if (!empty($data['id_grupo'])) {
            $this->arquivo_model->set_state('id_grupo', $data['id_grupo']);
            $id_grupo = $this->empresa_model->get_entries_by_grupo($data['id_grupo']);

            $cnpjs = format_simple_array($id_grupo, 'cnpj');
            $this->arquivo_model->set_state('cnpjs', $cnpjs);
        }

        $data['destinatarios'] = $this->input->get('destinatarios') ? $this->input->get('destinatarios') : NULL;
        if (!empty($data['destinatarios'])) {
            $this->arquivo_model->set_state('destinatarios', $data['destinatarios']);
        }

        $data['nome_arquivos'] = $this->input->get('nome_arquivos') ? $this->input->get('nome_arquivos') : NULL;
        if (!empty($data['nome_arquivos'])) {
            $this->arquivo_model->set_state('nome_arquivos', $data['nome_arquivos']);
        }

        $data['id_arquivos'] = $this->input->get('id_arquivos') ? $this->input->get('id_arquivos') : NULL;
        if (!empty($data['id_arquivos'])) {
            $this->arquivo_model->set_state('id_arquivos', $data['id_arquivos']);
        }

        $data['autor'] = $this->input->get('autor') ? $this->input->get('autor') : NULL;
        if (!empty($data['autor'])) {
            $this->arquivo_model->set_state('autor', $data['autor']);
        }

        $data['tipo_arquivo'] = $this->input->get('tipo_arquivo') ? $this->input->get('tipo_arquivo') : NULL;
        if (!empty($data['tipo_arquivo'])) {
            $this->arquivo_model->set_state('tipo', $data['tipo_arquivo']);
        }

        if (sess_user_tipo() != 'colaborador') {
            $empresas = sess_user_companies_grupo();
            $cnpjs = format_simple_array($empresas, 'cnpj');

            if (empty($cnpjs)) {
                $cnpjs = sess_user_companies();
            }

            $this->arquivo_model->set_state('cnpjs', $cnpjs);
        }

        return $data;
    }

    /**
     * Monitoramento, visão cliente
     * @method  monitoramento
     * @return  void  Sem retornos
     */
    public function monitoramento()
    {
        ini_set("memory_limit", "2048M");
        $user_lang = sess_user_language();
        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $this->arquivo_model->clear_states();
        $data = $this->apply_filters_default($user_lang);

        $page   = $this->input->get('per_page');
        $limit  = 10;
        $offset = ($page > 0 ? $page - 1 : 0) * $limit;

        $this->lang->load('monitoramento', $user_lang);
        $this->load->library('pagination');

        if (
            empty($this->input->get('cnpj'))  &&
            sess_user_tipo() != 'colaborador' &&
            !empty($empresa->cnpj)
        ) {
            $data['cnpj'] = $empresa->cnpj;
        }

        $data['params'] = [
            'cnpj'            => $data['cnpj'],
            'status'          => $data['status'],
            'id_grupo'        => $data['id_grupo'],
            'data_inicio'     => $data['data_inicio'],
            'data_fim'        => $data['data_fim'],
            'destinatarios'   => $data['destinatarios'],
            'tipo_arquivo'    => $data['tipo_arquivo'],
            'nome_arquivos'   => $data['nome_arquivos'],
            'id_arquivos'     => $data['id_arquivos'],
            'autor'           => $data['autor']
        ];
        $http_query = http_build_query($data['params']);

        $total_entries = $this->arquivo_model->get_total_entries();

        $data['itens'] = array_map(function ($item) {
            $item->autor = '';
            try{
                if($autor = $this->usuario_model->get_entry($item->id_usuario))
                    $item->autor = $autor;
            }catch(Exception $e) {}

            return $item;
        }, $this->arquivo_model->get_entries($limit, $offset));

        $data['language']       = $user_lang;
        $data['total_entries']  = $total_entries;

        $data['grupos']        = $this->grupo_model->get_entries();
        $data['statuses']      = $this->arquivo_model->get_status_cpd_cliente();
        $data['tipos']         = $this->arquivo_model->get_tipos_arquivo();
        $data['destinatarios'] = $this->input->get('destinatarios');

        $config['base_url']          = base_url("frontend/enviar_arquivos/monitoramento?". $http_query);

        $config['first_link']        = $this->lang->line('pagination1');
        $config['last_link']         = $this->lang->line('pagination2');
        $config['next_link']         = $this->lang->line('pagination3');
        $config['prev_link']         = $this->lang->line('pagination4');

        $config['use_page_numbers']  = TRUE;
        $config['total_rows']        = $total_entries;
        $config['per_page']          = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);
        $data['pagination']     = $this->pagination->create_links();

        $this->breadcrumbs->push($this->lang->line('central_processamento'), '/frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('arquivos_enviados'), '/frontend/enviar_arquivos/monitoramento');

        $this->include_js(array(
            'jquery.doublescroll.js',
            '../frontend/js/sweetalert2.min.js'
        ));

        $this->include_css(array(
            '../frontend/css/enviar_arquivos.css',
            '../frontend/css/sweetalert2.min.css',
        ));

        $this->render('frontend/enviar_arquivos/monitoramento', $data);
    }

    /**
     * Utilizado para manter a sessão via AJAX durante
     * envio de arquivos muito grandes
     * @method  keep_session
     * @todo          Não remova o método, nada a fazer
     * @return  void  Sem retornos
     */
    public function keep_session()
    {
    }

    /**
     * Exportação de arquivos enviados
     * @method  exportar
     * @return  void  Sem retornos
     */
    public function exportar()
    {
        ini_set("memory_limit", "2048M");

        $user_lang = sess_user_language();

        $this->lang->load('xls_arquivos', $user_lang);

        $this->apply_filters_default();
        $itens = $this->arquivo_model->get_entries();

        $this->load->library('PHPExcel');

        $excelObj = new PHPExcel();

        $excelObj->getActiveSheet()->setTitle('Arquivos');

        $excelObj->getActiveSheet()->setCellValue('A1', $this->lang->line('arquivo'));
        $excelObj->getActiveSheet()->setCellValue('B1', $this->lang->line('grupo'));
        $excelObj->getActiveSheet()->setCellValue('C1', $this->lang->line('status'));
        $excelObj->getActiveSheet()->setCellValue('D1', $this->lang->line('tipo'));
        $excelObj->getActiveSheet()->setCellValue('E1', $this->lang->line('descricao'));
        $excelObj->getActiveSheet()->setCellValue('F1', $this->lang->line('data_envio'));
        $excelObj->getActiveSheet()->setCellValue('G1', $this->lang->line('id_arquivo'));
        $excelObj->getActiveSheet()->setCellValue('H1', $this->lang->line('destinatarios'));

        $excelObj->getActiveSheet()->getStyle('A1:H1')->getFont()->setBold(true);
        $excelObj->getActiveSheet()->getStyle('A1:H1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');
        $excelObj->getActiveSheet()->getStyle('A1:H1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        foreach (range('A', 'H') as $columnID) {
            $excelObj->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
            $excelObj->getActiveSheet()
                ->getStyle($columnID)
                ->getAlignment()
                ->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }

        foreach ($itens as $k => $item) {
            $i = $k + 2;

            $cell = 'A' . $i;
            $excelObj->getActiveSheet()->getCell($cell)->setValue($item->nome_arquivo);

            $cell = 'B' . $i;
            $grupos = $item->get_grupo();
            $grupos = format_simple_array($grupos, 'nome');
            $grupos_str = '';
            foreach ($grupos as $grupo) {
                $grupos_str .= empty($grupos_str) ? $grupo : ', ' . $grupo;
            };
            $excelObj->getActiveSheet()->getCell($cell)->setValue($grupos_str);

            $cell = 'C' . $i;
            $excelObj->getActiveSheet()->getCell($cell)->setValue(get_status_arquivo_cliente($item));

            $cell = 'D' . $i;

            $fileType = "";

            switch($item->get_tipo_arquivo()) {
                case 'B.O.M':
                    $fileType = $this->lang->line('bom');
                    break;
                case 'Outros':
                    $fileType = $this->lang->line('outros');
                    break;
                case 'Aduaneiro/Fiscal':
                    $fileType = $this->lang->line('aduaneiro');
                    break;
            }

            $excelObj->getActiveSheet()->getCell($cell)->setValue($fileType);

            $cell = 'E' . $i;
            $excelObj->getActiveSheet()->getCell($cell)->setValue($item->descricao);

            $cell = 'F' . $i;
            $excelObj->getActiveSheet()->getCell($cell)->setValue(date('d/m/Y H:i:s', strtotime($item->criado_em)));

            $cell = 'G' . $i;
            $excelObj->getActiveSheet()->getCell($cell)->setValue(!empty($item->id_arquivo_externo) ? $item->id_arquivo_externo : '-');

            $cell = 'h' . $i;
            $destinatarios = $item->get_destinatarios();
            $destinatarios_str = "";
            foreach ($destinatarios as $destinatario) {
                $destinatarios_str .= $destinatario->nome." (".$destinatario->email.") | ";
            }
            $excelObj->getActiveSheet()->getCell($cell)->setValue(rtrim($destinatarios_str, "| "));
        }

        $filename = 'arquivos_cpd_' . date('Ymd') . '.xlsx';
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($excelObj, 'Excel2007');
        $objWriter->save('php://output');
        exit();
    }

    /**
     * Verifica se os usuários destinatários são validos
     * @method  usuarios_validos
     * @var     bool  $usuarioValido  Se o usuario é valido
     * @return  void                  Sem retornos
     */
    public function usuarios_validos()
    {
        $usuarioValido = true;

        try {
            $destinatariosStr = $this->input->post('destinatarios');

            $destinatarios = explode(';', $destinatariosStr);

            foreach($destinatarios as $destinatario) {
                if (!empty($destinatario)) {
                    if (!$this->usuario_model->is_valid_becomex_user(
                        trim($destinatario)
                    )) {
                        $usuarioValido = false;
                        break;
                    }
                }
            }

            echo json_encode(array(
                'msg' => 'Sucesso!',
                'data' => array(
                    'usuarioValido' => $usuarioValido
                ),
                'error' => false
            ));
        } catch (Exception $e) {
            echo json_encode(array(
                'msg' => 'Ocorreu um erro',
                'error' => true
            ));
        }
    }

    /**
     * Aplica os filtros padrões de consulta para o model de usuarios
     * @method  apply_filters_default_usuarios
     * @return  void                Sem retornos
     */
    private function apply_filters_default_usuarios()
    {
        $data['busca'] = $this->input->get('busca');
        if (!empty($data['busca'])) {
            $this->usuario_model->set_state('filter.nome_usuario', $data['busca']);
        }
        if (!$this->input->is_set('busca')) {
            $data['busca'] = NULL;
        }

        $data['id_grupo'] = $this->input->get('id_grupo');
        if (!empty($data['id_grupo'])) {
            $this->usuario_model->set_state('filter.id_grupo', $data['id_grupo']);
        }
        if (!$this->input->is_set('id_grupo')) {
            $data['id_grupo'] = [];
        }

        $data['id_perfil'] = $this->input->get('id_perfil');
        if (!empty($data['id_perfil'])) {
            $this->usuario_model->set_state('filter.id_perfil', $data['id_perfil']);
        }
        if (!$this->input->is_set('id_perfil')) {
            $data['id_perfil'] = NULL;
        }

        $data['id_produto'] = $this->input->get('id_produto');
        if (!empty($data['id_produto'])) {
            $this->usuario_model->set_state('filter.id_produto', $data['id_produto']);
        }
        if (!$this->input->is_set('id_produto')) {
            $data['id_produto'] = [];
        }

        $data['status'] = $this->input->get('status');
        if (isset($data['status']) && $data['status'] != NULL) {
            $this->usuario_model->set_state('filter.status', $data['status']);
        }
        if (!$this->input->is_set('status')) {
            $data['status'] = NULL;
        }

        return $data;
    }

    /**
     * Usuários x clientes, visão cliente
     * @method  usuarios_x_clientes
     * @return  void  Sem retornos
     */
    public function usuarios_x_clientes()
    {
        $this->load->model(array(
            'grupo_model',
            'perfil_model',
            'produto_model'
        ));
        $this->load->library('pagination');

        $query_str = '';

        $page   = $this->input->get('per_page');
        $limit  = 10;

        $offset = ($page>0?$page-1:0)*$limit;

        $this->usuario_model->clear_states();
        $data = $this->apply_filters_default_usuarios();

        $data['params'] = [
            'busca'      => $data['busca'],
            'id_grupo'   => $data['id_grupo'],
            'id_perfil'  => $data['id_perfil'],
            'id_produto' => $data['id_produto'],
            'status'     => $data['status']
        ];
        $query_str = http_build_query($data['params']);

        $data['list'] = $this->usuario_model->get_entries($limit, $offset);

        $data['grupos']   = $this->grupo_model->get_all_entries();
        $data['perfis']   = $this->perfil_model->get_entries();
        $data['produtos'] = $this->produto_model->get_all_entries(TRUE);

        $total_entries         = $this->usuario_model->get_total_entries();
        $data['total_entries'] = $total_entries;

        $config['base_url']          = base_url("frontend/enviar_arquivos/usuarios_x_clientes?". $query_str);
        $config['use_page_numbers']  = TRUE;
        $config['total_rows']        = $total_entries;
        $config['per_page']          = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);
        $data['pagination'] = $this->pagination->create_links();

        $lang             = sess_user_language();
        $data['language'] = $lang;

        $this->lang->load('usuarios_x_clientes', $lang);

        $this->breadcrumbs->push($this->lang->line('central_processamento'), 'frontend/enviar_arquivos');
        $this->breadcrumbs->push($this->lang->line('usuarios_x_clientes'), 'frontend/enviar_arquivos/usuarios_x_clientes');

        $this->include_css('bootstrap-select/bootstrap-select.min.css');
        $this->include_js('bootstrap-select/bootstrap-select.min.js');

        $this->render('frontend/enviar_arquivos/usuarios_x_clientes', $data);
    }

    private function send_user_seguidor_completed_notification($arquivos_sistemas = array())
    {
        $this->load->model(array(
            'cpd/arquivo_model',
            'usuario_model',
            'grupo_model'
        ));
        $clientes = array();
        foreach ($arquivos_sistemas as $arquivos_sistema){
            $arquivos_sistema = json_decode($arquivos_sistema);
            $arquivos_sistema = $this->arquivo_model->get_entry($arquivos_sistema->id_arquivo);
            $clientes[$arquivos_sistema->cnpj_cliente][] = $arquivos_sistema;

        }

        if(!empty($clientes)){
            foreach($clientes as $cnpj => $arquivos){
                $this->send_email_seguidores($arquivos, $cnpj);
            }
        }

    }

    public function send_email_seguidores($entrys, $cnpj){
        $grupo = $this->grupo_model->get_grupo_by_cnpj(preg_replace('/\D/', '', $cnpj));

        if (is_null($grupo))
            return;

        $usuarios_seguidores = $this->usuario_model->get_rel_usuarios_grupo($grupo);

        $emails_copia = array();
        if (!empty($usuarios_seguidores)) {
            foreach($usuarios_seguidores as $userSegui){
                try{
                    if($usu = $this->usuario_model->get_entry($userSegui->id_usuario_seguidor)){
                        if ($usu->ativo == 0)
                        continue;
                        $emails_copia[$usu->id_usuario] = ['nome'=> $usu->nome, 'email' =>$usu->email];

                    }
                } catch(Exception $e){

                }
            }
        }

        if (!empty($emails_copia)) {
            foreach ($emails_copia as $id => $data){
                $template_email =
                    $this->load->view('templates/template_arquivos_seguidor_novo_envio', array('arquivos' => [$entrys], 'email' => $data['email'], 'nome' => $data['nome']), TRUE);

                $email_data = array(
                    'base_url' => config_item('online_url'),
                    'html_message' => $template_email
                );

                $body = $this->load->view('templates/basic_template', $email_data, TRUE);

                $this->load->library('email');

                $this->email->to($data['email']);
                $this->email->from(config_item('mail_from_cpd_addr'));
                $this->email->subject('[BECOMEX] - Arquivos carregados no portal');
                $this->email->message($body);
                $this->email->send();
            }

        }

    }
}
