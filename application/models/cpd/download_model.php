<?php
class Download_model_base extends Arquivo_model_base
{
    function __construct()
    {
        parent::class;
    }
}
class Download_model extends MY_Model
{
    private $_table = 'cpd_download_arquivo';

    function __construct()
    {
        parent::__construct();
    }

    private function set_default_filter()
    {
        if ($nome_arquivos = $this->get_state('filter.nome_arquivos')) {
            if (!is_array($nome_arquivos)) {
                $nome_arquivos = array_filter(array_map('trim', explode(';', $nome_arquivos)));
            }

            $query_group = "";

            foreach ($nome_arquivos as $index => $nome_arquivo) {
                $query_group .= "a.nome_arquivo LIKE '%$nome_arquivo%'";
                if ($index < (count($nome_arquivos) - 1)) $query_group .= " OR ";
            }

            $this->db->where($query_group);
        }

        if ($id_arquivos = $this->get_state('filter.id_arquivos')) {
            if (!is_array($id_arquivos)) {
                $id_arquivos = array_filter(array_map('trim', explode(';', $id_arquivos)));
            }

            $query_group = "";

            foreach ($id_arquivos as $index => $id_arquivo_externo) {
                $query_group .= "a.id_arquivo_externo LIKE '%$id_arquivo_externo%'";
                if ($index < (count($id_arquivos) - 1)) $query_group .= " OR ";
            }

            $this->db->where($query_group);
        }

        if ($cnpjs = $this->get_state('filter.cnpjs')) {
            $this->db->where_in('a.cnpj_cliente', $cnpjs);
        } else {
            if (
                empty($cnpjs)
                && !empty($this->input->get('id_grupo'))
            ) {
                $this->db->where_in('a.cnpj_cliente', [0]);
            }

            if (
                $this->get_state('cnpj')
                && !$this->input->is_cli_request()
            ) {
                $this->db->where('a.cnpj_cliente', $this->get_state('cnpj'));
            } else if (
                empty($cnpjs)
                && sess_user_tipo() != 'colaborador'
                && !$this->input->is_cli_request()
            ) {
                $this->db->where('a.cnpj_cliente', 0);
            }
        }

        if ($autor = $this->get_state('filter.autor')) {
            $this->db->where("(
                u.nome LIKE '%$autor%'
                OR u.email LIKE '%$autor%'
            )", null, false);
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            if (
                !empty($this->get_state('filter.language')
                    && $this->get_state("language") != "english")
            ) {
                $data_ini = str_replace('/', '-', $data_ini);
            }

            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->db->where("a.criado_em >=", $data_ini);
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            if (
                !empty($this->get_state('filter.language')
                    && $this->get_state("language") != "english")
            ) {
                $data_fim = str_replace('/', '-', $data_fim);
            }

            $data_fim = date('Y-m-d 23:59:59', strtotime($data_fim));

            $this->db->where("a.criado_em <=", $data_fim);
        }

        if ($data_ini_download = $this->get_state('filter.data_ini_download')) {
            if (
                !empty($this->get_state('filter.language')
                    && $this->get_state("language") != "english")
            ) {
                $data_ini_download = str_replace('/', '-', $data_ini_download);
            }

            $data_ini_download = date('Y-m-d', strtotime($data_ini_download));

            $this->db->where("ad.created_at >=", $data_ini_download);
        }

        if ($data_fim_download = $this->get_state('filter.data_fim_download')) {
            if (
                !empty($this->get_state('filter.language')
                    && $this->get_state("language") != "english")
            ) {
                $data_fim_download = str_replace('/', '-', $data_fim_download);
            }

            $data_fim_download = date('Y-m-d 23:59:59', strtotime($data_fim_download));

            $this->db->where("ad.created_at <=", $data_fim_download);
        }

        if ($dias_restantes = $this->get_state('filter.dias_restantes')) {
            $this->db->having('dias_restantes', $dias_restantes);
        }


        if ($destinatarios = $this->get_state('filter.destinatarios')) {
            if (!is_array($destinatarios)) {
                $destinatarios = array_filter(array_map('trim', explode(';', $destinatarios)));
            }

            $query_group = "";

            foreach ($destinatarios as $index => $destinatario) {
                $query_group .= "usuario.email LIKE '%$destinatario%'";
                if ($index < (count($destinatarios) - 1)) $query_group .= " OR ";
            }

            if (!empty($destinatarios)) {
                $this->db->where("EXISTS (
                    SELECT 1 FROM cpd_arquivo_rel_destinatario card
                    INNER JOIN usuario ON usuario.id_usuario = card.id_usuario
                    WHERE a.id_arquivo = card.id_arquivo
                    AND ($query_group)
                )", NULL, FALSE);
            }
        }
    }

    public function get_entries($limit = NULL, $offset = NULL)
    {
        $dias_expiracao_download_cpd_arquivos = config_item('dias_expiracao_download_cpd_arquivos');
        $this->set_default_filter();
        $this->db->select('a.*, u.*, ad.created_at AS data_download');
        $this->db->select("($dias_expiracao_download_cpd_arquivos - (DATEDIFF(NOW(), a.criado_em))) AS dias_restantes", false);
        $this->db->join('usuario u', 'u.id_usuario = ad.id_usuario', 'left');
        $this->db->join('cpd_arquivo a', 'a.id_arquivo = ad.id_arquivo', 'right');
        $this->db->where('u.id_usuario = ad.id_usuario AND a.id_arquivo = ad.id_arquivo');
        $this->db->order_by("data_download", "DESC");
        $query = $this->db->get($this->_table . ' ad', $limit, $offset);
        return $query->result('Download_model_base');
    }

    public function get_total_entries()
    {
        $this->db->select('COUNT(*) as total', FALSE);
        $this->db->join('usuario u', 'u.id_usuario = ad.id_usuario', 'left');
        $this->db->join('cpd_arquivo a', 'a.id_arquivo = ad.id_arquivo', 'right');
        $this->db->where('u.id_usuario = ad.id_usuario AND a.id_arquivo = ad.id_arquivo');
        $query = $this->db->get($this->_table . ' ad');
        return $query->row()->total;
    }

    public function send_mail_consultor($arquivo)
    {
        $consultor    = $this->usuario_model->get_entry(sess_user_id());
        $cliente      = $this->usuario_model->get_entry($arquivo->id_usuario);
        $cliente_lang = sess_user_language($cliente->id_usuario);

        $this->lang->load('arquivos_cliente', $cliente_lang);

        $template_data = [
            'to'         => $cliente->nome,
            'from'       => $consultor->nome,
            'to_email'   => $cliente->email,
            'from_email' => $consultor->email,
            'arquivo'    => $arquivo
        ];

        $template_email = $this->load->view("templates/template_download_arquivo_consultor_" . $cliente_lang, $template_data, TRUE);

        $this->send($template_email, [
            'cliente_lang' => $cliente_lang,
            'arquivo'      => $arquivo,
            'to'           => $cliente->email
        ]);
    }

    private function send($body = '', $args = [])
    {
        $this->lang->load('arquivos_cliente', $args['cliente_lang']);

        $this->load->model([
            'cpd/arquivo_model',
            'empresa_model'
        ]);

        $arquivo = $args['arquivo'];

        $empresa = $this->empresa_model->get_empresas_by_cnpj($arquivo->cnpj_cliente);

        $this->load->library('email');

        $this->email->initialize([
            'mailtype' => 'text'
        ]);

        $this->email->to($args['to']);

        $this->email->from(
            config_item('mail_from_cpd_addr')
        );

        $this->email->subject("[Portal DWTAX] - {$this->lang->line('send_mail_consultor_title')} {$empresa->razao_social}");

        $this->email->message($body);

        $this->email->send(config_item('skip_job_2fa'));

        $this->db->insert($this->_table, [
            'id_usuario' => sess_user_id(),
            'id_arquivo' => $arquivo->id_arquivo,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function download_arquivo($id_arquivo_cpd, $args = [])
    {
        $this->load->model('cpd/processamento_model');

        $url = $this->config->item('url_api_download') . '?' . http_build_query([
            'idArquivo'     => $id_arquivo_cpd,
            'UsuarioPortal' => sess_user_id()
        ]);

        return $this->processamento_model->default_request($url, $args, true);
    }
}
