<?php

class Processamento_model extends CI_Model
{
    const UPLOAD                   = 'Aguardando Upload';
    const INICIADO                 = 'Iniciado Upload';
    const CANCELADO                = 'Cancelado';
    const PROGRESSO                = 'Progresso';
    const CONCLUIDO                = 'Concluido Upload';
    const UPLOAD_FALHA             = 'Falha no Upload';

    const AGUARDANDO               = 'Aguardando Processamento';
    const PROCESSANDO              = 'Em Processamento';

    const ENTREGUE                 = 'Entregue com Êxito';
    const MOVIDO                   = 'Movido para CPD';

    const ENTREGUE_FALHA           = 'Falha na Entrega';
    const FALHA                    = 'Falha no Envio para CPD';

    const REPROCESSAMENTO          = 'Reprocessamento';

    const PROCESSADO_COM_ERRO_CPD = 'ProcessadoComErro';
    const PROCESSADO_COM_SUCESSO_CPD = 'ProcessadoComSucesso';
    const PROCESSADO_COM_ALERTA_CPD = 'ProcessadoComAlerta';
    const VALIDADO_OK_CPD = 'ValidadoOk';
    const MOVIDO_QUARENTENA_CPD = 'MovidoQuarentena';
    const ENVIADO_EXPURGO_CPD = 'EnviadoExpurgo';
    const EM_VALIDACAO_CPD = 'EmValidacao';
    const PREPARANDO_CPD = 'Preparando';
    const PROCESSAMENTO_INTERROMPIDO_CPD = 'ProcessamentoInterrompido';
    const PENDENTE_CPD = 'Pendente';
    const PROCESSANDO_CPD = 'Processando';
    const PENDENTE_VALIDACAO_CPD = 'PendenteValidacao';

    private $status_cpd = [
        [
            'status' => [99],
            'statusDescricao' => 'ProcessamentoInterrompido',
            'statusVisaoCliente' => 'Validado CPD',
            'statusVisaoAdmin' => 'Processamento Interrompido',
        ],
        [
            'status' => [3],
            'etapa' => 1,
            'statusDescricao' => 'ProcessadoComErro',
            'statusVisaoCliente' => 'Em Análise CPD',
            'statusVisaoAdmin' => 'Processado Com Erro',
        ],
        [
            'status' => [11],
            'statusDescricao' => 'ProcessadoComAlerta',
            'statusVisaoCliente' => 'Em Análise CPD',
            'statusVisaoAdmin' => 'Processado Com Alerta',
        ],
        [
            'status' => [5],
            'etapa' => 1,
            'statusDescricao' => 'MovidoQuarentena',
            'statusVisaoCliente' => 'Em Análise CPD',
            'statusVisaoAdmin' => 'Movido Quarentena',
        ],
        [
            'status' => [7],
            'etapa' => 1,
            'statusDescricao' => 'EnviadoExpurgo',
            'statusVisaoCliente' => 'Em Análise CPD',
            'statusVisaoAdmin' => 'Enviado Expurgo'
        ],
        [
            'status' => [2],
            'etapa' => 1,
            'statusDescricao' => 'EmValidacao',
            'statusVisaoCliente' => 'Em Validação CPD',
            'statusVisaoAdmin' => 'Em Validação CPD',
        ],
        [
            'status' => [1],
            'etapa' => 1,
            'statusDescricao' => 'PendenteValidacao',
            'statusVisaoCliente' => 'Em Validação CPD',
            'statusVisaoAdmin' => 'Em Validação CPD',
        ],
        [
            'status' => [4],
            'etapa' => 1,
            'statusDescricao' => 'ProcessadoComSucesso',
            'statusVisaoCliente' => 'Validado CPD',
            'statusVisaoAdmin' => 'Processado Com Sucesso',
        ],
        [
            'status' => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            'etapa' => 2,
            'statusDescricao' => 'ValidadoOk',
            'statusVisaoCliente' => 'Validado CPD',
            'statusVisaoAdmin' => 'Validado CPD',
        ],
        [
            'status' => [98],
            'statusDescricao' => 'Preparando',
            'statusVisaoCliente' => 'Em Validação CPD',
            'statusVisaoAdmin' => 'Em Validação CPD',
        ],
        [
            'status' => [1],
            'statusDescricao' => 'Pendente',
            'statusVisaoCliente' => 'Validado CPD',
            'statusVisaoAdmin' => 'Validado CPD',
        ],
        [
            'status' => [2],
            'statusDescricao' => 'Processando',
            'statusVisaoCliente' => 'Validado CPD',
            'statusVisaoAdmin' => 'Validado CPD',
        ]
    ];

    private $arquivos = array();
    private $has_fail = false;
    private $maxAttempts = 8;

    public function __construct()
    {
        parent::__construct();

        $this->load->model(array(
            'empresa_model',
            'cpd/slack_model',
            'cpd/arquivo_model',
            'cpd/arquivo_log_model',
            'cpd/arquivo_grupo_model',
            'cpd/processamento_api_model',
            'grupo_model',
            'usuario_model'
        ));
    }

    protected function getStatusCpd()
    {
        return $this->status_cpd;
    }

    public function processar()
    {
        $this->arquivo_model->set_state('status_processamento', self::AGUARDANDO);
        $entries = $this->arquivo_model->get_entries();

        if (empty($entries)) {
            throw new Exception('Nenhum arquivo para processar');
        }

        foreach ($entries as $entry) {

            $item = $this->arquivo_model->get_entry($entry->id_arquivo);
            if (!empty($item) && $item->status_processamento == self::AGUARDANDO && !$this->arquivo_log_model->check_log_exists($entry->id_arquivo, self::PROCESSANDO)) {

                $this->arquivos[$entry->id_arquivo] = $entry;

                $this->log_status_processamento($entry, self::PROCESSANDO);
                $this->log_envio($entry, self::PROCESSANDO);

                $this->send_file($entry);
            }
        }

        $this->enviar_email();
        $this->enviar_email_seguidores();
        $this->garbage_collector();
    }

    public function processar_arquivo($id_arquivo)
    {
        if (empty($id_arquivo)) throw new Exception('ID do arquivo não informado');

        $this->arquivo_model->clear_states();

        $entry = $this->arquivo_model->get_entry($id_arquivo);

        $this->log_envio($entry, self::REPROCESSAMENTO);

        $this->send_file($entry, TRUE);

        if (empty($this->arquivos[$id_arquivo]->situacao_envio)) {
            throw new Exception('Não foi possível realizar o envio do arquivo');
        }

        $this->enviar_email();
        $this->enviar_email_seguidores();
        $this->garbage_collector();
    }

    public function processar_arquivos_remanescentes()
    {
        $this->arquivo_model->clear_states();

        $arquivos = $this->arquivo_model->get_arquivos_remanescentes();

        foreach ($arquivos as $arquivo) {
            $this->log_envio($arquivo, self::REPROCESSAMENTO);

            $this->send_file($arquivo, TRUE);
        }

        $this->garbage_collector();
    }

    private function log_envio($entry = NULL, $tipo = NULL, $extra = '')
    {
        if (empty($entry) || empty($tipo)) return FALSE;

        $dbdata = array(
            'id_arquivo'   => $entry->id_arquivo,
            'id_usuario'   => $entry->id_usuario,
            'cnpj_cliente' => $entry->cnpj_cliente,
            'evento'       => $tipo,
            'extra'        => $extra,
            'nome_arquivo' => $entry->nome_arquivo,
            'criado_em'    => date('Y-m-d H:i:s', time())
        );

        return $this->arquivo_log_model->save($dbdata);
    }

    private function log_status_processamento($entry = NULL, $tipo = NULL, $id_arquivo_externo = NULL, $args = array())
    {
        if (empty($entry) || empty($tipo)) {
            return FALSE;
        }

        $where = array(
            'id_arquivo' => $entry->id_arquivo
        );

        $insert_data = array(
            'tentativas_processamento' => $entry->tentativas_processamento,
            'status_processamento' => $tipo
        );

        if ($id_arquivo_externo !== NULL) {
            $insert_data['id_arquivo_externo'] = $id_arquivo_externo;
        }

        $this->arquivo_model->save($insert_data, $where);
    }

    private function enviar_email_prepare_data()
    {
        $data = array();

        foreach ($this->arquivos as $i => $entry) {
            if (empty($data[$entry->cnpj_cliente])) {
                $data[$entry->cnpj_cliente] = new StdClass;
                $data[$entry->cnpj_cliente]->total = 0;
                $data[$entry->cnpj_cliente]->arquivos = array();
            }

            if (empty($data[$entry->cnpj_cliente]->empresa)) {
                $data[$entry->cnpj_cliente]->empresa = $this->empresa_model->get_entry_by_cnpj($entry->cnpj_cliente);
            }

            /* Busca a versão atualizada do banco do arquivo para impedir inconsistência dos dados */
            $entry = $this->arquivo_model->get_entry($entry->id_arquivo);

            if ($entry->status_processamento == self::ENTREGUE_FALHA) $this->has_fail = true;

            $data[$entry->cnpj_cliente]->arquivos[$entry->status_processamento][$i] = $entry;
            $data[$entry->cnpj_cliente]->total++;
        }

        return $data;
    }

    private function enviar_email()
    {
        $this->load->library('email');
        $entries = $this->enviar_email_prepare_data();
        if (!empty($entries)) {
            $data = array(
                'entries' => $entries
            );

            $body = $this->load->view('templates/basic_template', array(
                'base_url' => $this->config->item('online_url'),
                'html_message' => $this->load->view('templates/email_processamento', $data, TRUE)
            ), TRUE);

            $this->email->subject('[Portal DWTAX] - ' . ($this->has_fail ? 'Erro - ' : '') . ' Processamento de Arquivos');
            $this->email->to($this->config->item('mail_cpd_addr'));
            $this->email->from($this->config->item('mail_from_cpd_addr'));
            $this->email->message($body);

            $this->email->send();
        }
    }

    private function enviar_email_seguidores()
    {
        $this->load->library('email');
        $entries = $this->enviar_email_prepare_data();
        if (!empty($entries)) {
            $data = array(
                'entries' => $entries
            );

            $clientes = array();
            foreach ($entries as $entry) {
                $tem = true;
                if (count($clientes)) {
                    foreach ($clientes as $cliente) {
                        foreach ($cliente->entries as $entry2) {
                            if ($entry2->cnpj_cliente == $entry->cnpj_cliente) {
                                $cliente->entries[] = $entry;
                                $tem = false;
                            }
                        }
                    }
                    if ($tem) {
                        $clientes[]->entries[] = $entry;
                    }
                } else {
                    $clientes[]->entries[] = $entry;
                }
            }

            $this->load->library('email');
            foreach ($clientes as $cliente) {
                if (!empty($cliente->entries)) {
                    $data = array(
                        'entries' => $cliente->entries
                    );

                    $grupo = null;
                    foreach ($cliente->entries as $arquivo) {
                        if ($arquivo) {
                            foreach ($arquivo->arquivos as $status => $arr_arquivos) {
                                if ($status == 'Entregue com Êxito') {
                                    foreach ($arr_arquivos as $archiveKey => $archiveData) {
                                        if (is_null($grupo))
                                            $grupo = $this->grupo_model->get_grupo_by_cnpj($archiveData->cnpj_cliente);
                                    }
                                }
                            }
                        }
                    }

                    if (is_null($grupo))
                        continue;

                    $usuarios_seguidores = $this->usuario_model->get_rel_usuarios_grupo($grupo);


                    $email_principal = '';
                    $emails_copia = array();
                    if (!empty($usuarios_seguidores)) {
                        foreach ($usuarios_seguidores as $userSegui) {
                            try {
                                if ($usu = $this->usuario_model->get_entry($userSegui->id_usuario_seguidor)) {
                                    if ($usu->ativo == 0)
                                        continue;
                                    if (empty($email_principal)) {
                                        $email_principal = $usu->email;
                                    } else {
                                        $emails_copia[] = $usu->email;
                                    }
                                }
                            } catch (Exception $e) {
                            }
                        }
                    }
                    if (!empty($email_principal)) {
                        $body = $this->load->view('templates/basic_template', array(
                            'base_url' => $this->config->item('online_url'),
                            'html_message' => $this->load->view('templates/email_processamento_novo', $data, TRUE)
                        ), TRUE);

                        $this->email->subject('[Portal DWTAX] - ' . ($this->has_fail ? 'Erro - ' : '') . ' Processamento de Arquivos');
                        $this->email->to($this->config->item('mail_cpd_addr'));
                        // $this->email->from($email_principal);
                        $this->email->from(config_item('mail_from_cpd_addr'));
                        $this->email->cc($emails_copia);
                        $this->email->message($body);

                        $this->email->send();
                    }
                }
            }
        }
    }

    private function enviar_email_falha_remanescentes($arquivo)
    {
        if (!empty($arquivo)) {
            $usuario = $arquivo->get_usuario();

            if (!empty($usuario)) {
                $this->load->library('email');

                $message  = "<h4>Olá {$usuario->nome},</h4>";
                $message .= "<p>Informamos que não foi possível realizar o upload do arquivo $arquivo->nome_arquivo.</p>";

                $body = $this->load->view('templates/basic_template', array(
                    'html_message' => $message,
                    'base_url' => config_item('online_url')
                ), TRUE);

                $this->email->subject('[Portal DWTAX] - Falha no envio de arquivos');

                $this->email->to($usuario->email);
                $this->email->from($this->config->item('mail_from_cpd_addr'));
                $this->email->message($body);

                $this->email->send();
            }
        }
    }

    private function get_arquivo_path($entry, $relative = FALSE)
    {
        $upload_path = $this->config->item('upload_arquivos_cpd_path');
        $file_path  = FCPATH
            . $upload_path
            . $entry->cnpj_cliente
            . DIRECTORY_SEPARATOR
            . $entry->nome_arquivo;

        return $file_path;
    }

    private function get_token()
    {
        try {
            $ch = curl_init();

            $url = $this->config->item('url_api_upload_token');

            $dataString = json_encode(array(
                'username' => $this->config->item('ws_cpd_username'),
                'password' => $this->config->item('ws_cpd_password')
            ));

            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);

            $headers = array(
                "Content-Type: application/json",
                'Content-Length: ' . strlen($dataString)
            );

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, TRUE);
            curl_setopt($ch, CURLOPT_TIMEOUT, 9999);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_FAILONERROR, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);

            $response = curl_exec($ch);
            $response = json_decode($response);
            // $error = curl_error($ch);

            if (!isset($response->token) && empty($response->token)) {
                throw new Exception('Não foi possível obter o token de acesso.');
            }

            return $response->token;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    private function send_file_req($entry)
    {
        $this->load->helper('file');

        $ch = curl_init();

        $url = $this->config->item('url_api_upload_cpd');

        $file_path = $this->get_arquivo_path($entry);

        if (!file_exists($file_path)) {
            throw new Exception('Arquivo não encontrado');
        }

        $entry->file_path = $file_path;

        $usuario = $this->usuario_model->get_entry($entry->id_usuario);

        $destinatarios = $entry->get_destinatarios();

        $destinatarios = array_map(function ($item) {
            return $item->email;
        }, $destinatarios);

        $postFields = array(
            'Files' => new cURLFile($file_path, get_mime_by_extension($file_path), $entry->nome_arquivo),
            'DocumentoJson' => json_encode(array(
                'UsuarioPortal'        => $usuario->email,
                'TipoOrigem'           => $entry->get_status_becomex(),
                'Observacao'           => $entry->descricao,
                'Cnpj'                 => $entry->cnpj_cliente,
                "UsuarioDestinatario"  => implode(",", $destinatarios)
            ))

        );

        $token = $this->get_token();

        $headers = array(
            'Authorization: ' . $token
        );

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 9999);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_FAILONERROR, TRUE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,  1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);

        $response = curl_exec($ch);
        $response = json_decode($response);

        $error = curl_error($ch);

        curl_close($ch);

        return array(
            'response' => $response,
            'error' => $error
        );
    }

    private function send_file($entry, $retry = FALSE)
    {
        set_time_limit(0);

        try {

            $res = $this->send_file_req($entry);

            $response = $res['response'];
            $error = $res['error'];

            try {
                $entry->tentativas_processamento++;

                $empresa = $this->empresa_model->get_entry_by_cnpj($entry->cnpj_cliente);
                $usuario = $entry->get_usuario();

                if (!empty($error)) {
                    if ($entry->tentativas_processamento >= 0 || $retry) {
                        $this->processamento_api_model->insert_log(array(
                            'empresa' => $empresa,
                            'usuario' => $usuario,
                            'arquivo' => $entry,
                            'error' => $error
                        ));
                    }

                    if ($entry->tentativas_processamento >= 7) {
                        $this->enviar_email_falha_remanescentes($entry);
                    }

                    throw new Exception('Falha no Processamento');
                }

                $sucesso  = $response[0]->sucesso;
                $id_arquivo_cpd = !empty($response[0]->idArquivoCPD) ? $response[0]->idArquivoCPD : NULL;

                if (is_null($id_arquivo_cpd)) {
                    $id_arquivo_cpd = !empty($response[0]->idArquivoCpd) ? $response[0]->idArquivoCpd : NULL;
                }

                if (!$id_arquivo_cpd && $sucesso) {
                    throw new Exception('Falha no envio');
                }

                if ($sucesso) {
                    $this->log_status_processamento($entry, self::ENTREGUE, $id_arquivo_cpd);
                    $this->log_envio($entry, self::MOVIDO);
                } else {

                    $this->processamento_api_model->insert_log(array(
                        'empresa' => $empresa,
                        'usuario' => $usuario,
                        'arquivo' => $entry,
                        'error' => isset($response->mensagem) ? $response->mensagem : 'API retornou algum erro que não estava mapeado'
                    ));

                    $this->log_status_processamento($entry, self::ENTREGUE_FALHA, 0);
                    $this->log_envio($entry, self::FALHA);
                }

                $entry->situacao_envio = $sucesso;
            } catch (Exception $e) {
                $this->update_tentativa($entry, $retry);
            }
        } catch (Exception $e) {
            $entry->situacao_envio = false;
        }

        $this->arquivos[$entry->id_arquivo] = $entry;
    }

    private function update_tentativa($entry, $retry = FALSE)
    {
        if ($entry->tentativas_processamento > 5 && !$retry) throw new Exception('Limite atingido');

        $this->log_status_processamento($entry, self::ENTREGUE_FALHA);
        $this->log_envio($entry, self::FALHA, $entry->tentativas_processamento <= 5 ? 'Tentativa ' . $entry->tentativas_processamento . '/5' : $entry->tentativas_processamento . '&ordf; tentativa');

        if ($retry) throw new Exception('Não foi possível realizar o envio do arquivo');

        sleep(15); // #11540 - Implementado delay de 15 segundos entre requisições

        $this->send_file($entry, $retry);
    }

    private function garbage_collector()
    {
        foreach ($this->arquivos as $arquivo) {
            if (!$arquivo->situacao_envio) {
                continue;
            }

            unlink($arquivo->file_path);
        }
    }

    /**
     * ========================================================
     * NOTIFICAÇÃO DE UPLOADS INCOMPLETOS
     * ========================================================
     */
    public function incompletos()
    {
        $this->arquivo_model->clear_states();
        $this->arquivo_model->set_state('incompletos', TRUE);
        $this->arquivos = $this->arquivo_model->get_entries();

        if (empty($this->arquivos)) throw new Exception('Nenhum arquivo encontrado');

        $this->incompletos_bcx();
        $this->incompletos_usr();
        $this->set_email_enviado();
    }

    private function incompletos_bcx()
    {
        if (empty($this->arquivos)) return FALSE;

        $this->load->library('email');
        $data = array(
            'entries' => $this->enviar_email_prepare_data()
        );
        $html = $this->load->view('templates/email_uploads_incompletos', $data, TRUE);

        $email_data = array();
        $email_data['base_url'] = $this->config->item('online_url');
        $email_data['html_message'] = $html;

        $body = $this->load->view('templates/basic_template', $email_data, TRUE);

        $this->email->subject('Beconnect - Uploads incompletos');
        $this->email->to($this->config->item('mail_cpd_addr'));
        $this->email->from($this->config->item('mail_from_cpd_addr'));
        $this->email->message($body);

        $this->email->send();
    }

    private function _enviar_email_usr_prepare_data($entry = NULL)
    {
        if (empty($entry)) return NULL;

        $user_lang = sess_user_language();

        $this->lang->load('email_uploads_incompletos', $user_lang);

        $this->load->library('email');
        $data = array('entry' => $entry);
        $html = $this->load->view('templates/email_uploads_incompletos_usr', $data, TRUE);

        $email_data = array();
        $email_data['base_url'] = $this->config->item('online_url');
        $email_data['html_message'] = $html;

        $body = $this->load->view('templates/basic_template', $email_data, TRUE);

        $this->email->subject($this->lang->line('assunto'));
        $this->email->to($entry->usuario->email);
        $this->email->from($this->config->item('mail_from_cpd_addr'));
        $this->email->message($body);

        $this->email->send();
    }

    private function enviar_email_usr_prepare_data()
    {
        if (empty($this->arquivos)) return FALSE;

        $data = array();

        foreach ($this->arquivos as $entry) {
            if (empty($data[$entry->id_usuario])) {
                $data[$entry->id_usuario] = new StdClass();
                $data[$entry->id_usuario]->usuario = $entry->get_usuario();
                $data[$entry->id_usuario]->arquivos = array();
            }

            $data[$entry->id_usuario]->arquivos[] = $entry;
        }

        return $data;
    }

    private function incompletos_usr()
    {
        if (empty($this->arquivos)) return FALSE;

        $entries = $this->enviar_email_usr_prepare_data();

        if (empty($entries)) return FALSE;

        foreach ($entries as $entry) {
            $this->_enviar_email_usr_prepare_data($entry);
        }
    }

    private function set_email_enviado()
    {
        if (empty($this->arquivos)) return FALSE;

        foreach ($this->arquivos as $entry) {
            $this->arquivo_model->save(array(
                'status_processamento' => self::UPLOAD_FALHA,
                'email_enviado' => 1
            ), array(
                'id_arquivo' => $entry->id_arquivo
            ));
        }
    }

    private function update_email_enviado($id_arquivo, $status)
    {
        if (!empty($id_arquivo)) {
            $this->arquivo_model->save(array(
                'email_quarentena' => $status
            ), array(
                'id_arquivo' => $id_arquivo
            ));
        }
    }

    public function atualizar_email_quarentena($entries = array())
    {
        try {
            if (!empty($entries)) {
                foreach ($entries as $value) {
                    $this->update_email_enviado($value->id_arquivo, 1);
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public function enviar_email_quarentena_clientes_grupo_seguidor($entries = array())
    {
        $clientes = array();
        foreach ($entries as $entry) {
            $tem = true;
            if (count($clientes)) {
                foreach ($clientes as $cliente) {
                    foreach ($cliente->entries as $entry2) {
                        if ($entry2->cnpj_cliente == $entry->cnpj_cliente) {
                            $cliente->entries[] = $entry;
                            $tem = false;
                        }
                    }
                }
                if ($tem) {
                    $clientes[]->entries[] = $entry;
                }
            } else {
                $clientes[]->entries[] = $entry;
            }
        }

        $this->load->library('email');
        foreach ($clientes as $cliente) {
            if (!empty($cliente->entries)) {
                $data = array(
                    'entries' => $cliente->entries
                );

                $grupo = null;
                foreach ($cliente->entries as $arquivo) {
                    $grupo = $this->grupo_model->get_grupo_by_cnpj($arquivo->cnpj_cliente);
                }

                $usuarios_seguidores = $this->usuario_model->get_rel_usuarios_grupo($grupo);

                $email_principal = '';
                $emails_copia = array();
                if (!empty($usuarios_seguidores)) {
                    for ($i = 0; $i < count($usuarios_seguidores); $i++) {
                        if ($i == 0) {
                            $email_principal = $this->usuario_model->get_entry($usuarios_seguidores[$i]->id_usuario_seguidor)->email;
                            unset($usuarios_seguidores[$i]);
                        } else {
                            $emails_copia[] = $this->usuario_model->get_entry($usuarios_seguidores[$i]->id_usuario_seguidor)->email;
                        }
                    }
                }

                if (!empty($email_principal)) {
                    $html = $this->load->view('templates/email_processamento_quarentena', $data, TRUE);

                    $email_data = array();
                    $email_data['base_url'] = $this->config->item('online_url');
                    $email_data['html_message'] = $html;

                    $body = $this->load->view('templates/basic_template', $email_data, TRUE);

                    $this->email->subject('Beconnect - Arquivos Movidos para Quarentena');
                    $this->email->to($email_principal);
                    $this->email->cc($emails_copia);
                    $this->email->from($this->config->item('mail_from_cpd_addr'));
                    $this->email->message($body);

                    $this->email->send();
                }
            }
        }
    }

    public function enviar_email_quarentena($entries = array())
    {
        $this->load->library('email');
        $data = array(
            'entries' => $entries
        );

        $html = $this->load->view('templates/email_processamento_quarentena', $data, TRUE);

        $email_data = array();
        $email_data['base_url'] = $this->config->item('online_url');
        $email_data['html_message'] = $html;

        $body = $this->load->view('templates/basic_template', $email_data, TRUE);

        $this->email->subject('Beconnect - Arquivos Movidos para Quarentena');
        $this->email->to($this->config->item('mail_cpd_addr'));
        $this->email->from($this->config->item('mail_from_cpd_addr'));
        $this->email->message($body);

        $this->email->send();
    }

    public function get_arquivos_quarentena()
    {
        $this->load->helper('file');

        $date = date('Y/m/d H:i');

        $url = $this->config->item('url_api_upload_consultar_quarentena') . '?' . http_build_query(array(
            'dataInicial' => date('Y/m/d H:i', strtotime('-10 day', strtotime($date))),
            'dataFinal' => $date
        ));

        return $this->default_request($url, array('post' => true));
    }

    private function ws_get_status_arquivo($dataString)
    {
        $url = $this->config->item('url_api_upload_consultar_status');

        return $this->default_request($url, array(
            'dataString' => $dataString,
            'post' => true
        ));
    }

    public function get_status_arquivos($entries)
    {
        try {
            if (empty($entries))
                throw new Exception('É necessário informar um arquivo.');

            $this->load->helper('file');
            $idsArquivos = array();
            foreach ($entries as $entry) {
                $idsArquivos[] = $entry->id_arquivo_externo;
            }

            $dataString = json_encode($idsArquivos);

            return $this->ws_get_status_arquivo($dataString);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public function get_status_arquivo($id_arquivo)
    {
        try {
            $entry = $this->arquivo_model->get_entry($id_arquivo);

            if (empty($id_arquivo))
                throw new Exception('É necessário informar um arquivo.');

            $this->load->helper('file');
            $dataString = json_encode(array(
                $entry->id_arquivo_externo
            ));

            return !empty($entry->id_arquivo_externo) ? $this->ws_get_status_arquivo($dataString) : [];
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    protected function getStatusDescricaoCpd($status = NULL, $etapa)
    {
        foreach ($this->getStatusCpd() as $statusCpd) {
            if (in_array($status, $statusCpd['status']) && $etapa == $statusCpd['etapa']) {
                $status = $statusCpd['statusDescricao'];
            }
        }

        return $status;
    }

    public function showStatusDescricao($status, $visao = 'cliente')
    {
        foreach ($this->getStatusCpd() as $statusCpd) {
            if ($status == $statusCpd['statusDescricao']) {
                if ($visao == 'cliente') return $statusCpd['statusVisaoCliente'];
                if ($visao == 'admin') return $statusCpd['statusVisaoAdmin'];
            }
        }

        return $status;
    }

    public function atualizar_status_processamento_arquivo($arquivos_cpd)
    {
        if (!is_array($arquivos_cpd)) {
            $arquivos_cpd = array($arquivos_cpd);
        }

        foreach ($arquivos_cpd as $arquivo) {
            if (!empty($arquivo->idArquivoCPD) && !empty($arquivo->nomeArquivo)) {
                if ($arquivo->situacao == 3) {
                    $this->arquivo_model->save(array(
                        'status_processamento' => $this->getStatusDescricaoCpd($arquivo->etapaStatus, $arquivo->etapa),
                        'situacao_integracao' => '1'
                    ), array(
                        'id_arquivo_externo' => $arquivo->idArquivoCPD
                    ));
                } else {
                    $this->arquivo_model->save(array(
                        'status_processamento' => $this->getStatusDescricaoCpd($arquivo->etapaStatus, $arquivo->etapa)
                    ), array(
                        'id_arquivo_externo' => $arquivo->idArquivoCPD
                    ));
                }
            } elseif (!empty($arquivo->idArquivoCpd) && !empty($arquivo->nomeArquivo)) {
                if ($arquivo->situacao == 3) {
                    $this->arquivo_model->save(array(
                        'status_processamento' => $this->getStatusDescricaoCpd($arquivo->etapaStatus, $arquivo->etapa),
                        'situacao_integracao' => '1'
                    ), array(
                        'id_arquivo_externo' => $arquivo->idArquivoCpd
                    ));
                } else {
                    $this->arquivo_model->save(array(
                        'status_processamento' => $this->getStatusDescricaoCpd($arquivo->etapaStatus, $arquivo->etapa)
                    ), array(
                        'id_arquivo_externo' => $arquivo->idArquivoCpd
                    ));
                }
            }
        }
    }

    public function atualizar_status_arquivo_iniciado_upload()
    {
        $arquivos = $this->arquivo_model->get_entries_upload_iniciado();

        foreach ($arquivos as $arquivo) {
            $logs = $this->arquivo_model->get_logs_arquivo($arquivo->id_arquivo);

            if (count($logs) == 1) {
                foreach ($logs as $log) {
                    $data_atual = strtotime(date('Y-m-d H:i'));
                    $data_log = strtotime($log->criado_em);

                    if ($data_atual > $data_log) {
                        $intervalo = $data_atual - $data_log;
                        if ($log->evento == 'Iniciado Upload' && $intervalo > 7200) {
                            if ($arquivo->status_processamento !== 'Falha no Upload') {
                                $this->arquivo_model->update_status_iniciado_upload_for_falha_no_upload($arquivo->id_arquivo);

                                $data = (object) array(
                                    'id_arquivo' => $arquivo->id_arquivo,
                                    'id_usuario' => $arquivo->id_usuario,
                                    'cnpj_cliente' => $arquivo->cnpj_cliente,
                                    'nome_arquivo' => $arquivo->nome_arquivo
                                );

                                $upload_path_root = getcwd() . DIRECTORY_SEPARATOR . config_item('upload_arquivos_cpd_path');
                                $upload_path = $upload_path_root . $arquivo->cnpj_cliente . DIRECTORY_SEPARATOR . $arquivo->nome_arquivo;

                                if (!empty($arquivo->cnpj_cliente) && !empty($arquivo->nome_arquivo)) {
                                    if (file_exists($upload_path)) {
                                        unlink($upload_path);
                                    }
                                }

                                $this->log_envio($data, 'Falha no Upload');
                            }
                        }
                    }
                }
            }
        }
    }

    public function default_request($url, $args = [], $isFile = false)
    {
        if ($isFile && isset($args['filepath']) && !empty($args['filepath'])) {
            $fp = fopen($args['filepath'], 'w+');
        }

        $ch = curl_init();

        $headers = [
            'Authorization: ' . $this->get_token()
        ];

        if (isset($args['dataString']) && !empty($args['dataString'])) {
            $headers[] = 'Content-Length: ' . strlen($args['dataString']);
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_POSTFIELDS, $args['dataString']);
        }

        if ($isFile && isset($args['filepath']) && !empty($args['filepath'])) {
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, (isset($args['post']) && $args['post']));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 150);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
        //curl_setopt($ch, CURLOPT_FAILONERROR, TRUE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);

        $response = curl_exec($ch);

        if (!$isFile) {
            $response = json_decode($response);
        }

        $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($httpStatusCode == 401) {
            throw new Exception('Não foi possível se autenticar com o servidor. Entre em contato com o Administrador.');
        }

        curl_close($ch);

        return $response;
    }
}
