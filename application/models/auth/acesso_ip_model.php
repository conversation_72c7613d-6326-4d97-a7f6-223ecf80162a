<?php

class Acesso_ip_model extends MY_Model {

    public $_table = 'ip';
    public $_table_log = 'acesso_ip';

    public function __construct()
    {
        parent::__construct();
    }

    protected function apply_default_filters()
    {
        if ($busca = $this->get_state('filter.busca')) {
            $this->db->like('a.ip', $busca);
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            $this->db->where("p.criado_em >=", format_date('Y-m-d 00:00:00', $data_ini));
            $this->db->where('criado_em >= (SELECT max(criado_em) FROM acesso_ip ab WHERE a.ip = ab.ip GROUP BY ab.ip)');
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->where("p.criado_em <=", format_date('Y-m-d 23:59:59', $data_fim));
            $this->db->where('criado_em <= (SELECT max(criado_em) FROM acesso_ip ab WHERE a.ip = ab.ip GROUP BY ab.ip)');
        }

        if ($situacao = $this->get_state('filter.situacao')) {
            if ($situacao == 2) {
                $this->db->where('a.permanently_block', true);
            }

            if ($situacao == 1) {
                $this->db->where('a.block_ip', true)->where('a.permanently_block', false);
            }

            if ($situacao == 3) {
                $this->db->where('a.block_ip', false)->where('a.permanently_block', false);
            }
        }
    }

    public function get_total_entries()
    {
        $this->apply_default_filters();

        return count($this->get_entries(NULL, NULL, TRUE));
    }

    public function get_entries($limit = NULL, $offset = NULL, $count = FALSE)
    {
        $this->apply_default_filters();


        $this->db->select('a.ip, max(p.criado_em) criado_em, a.block_ip, a.permanently_block');
        $this->db->join('acesso_ip p', 'p.ip = a.ip', 'inner');

        $this->db->group_by('a.ip');

        $this->db->order_by('p.criado_em', 'DESC');

        $query = $this->db->get($this->_table.' a', $limit, $offset);

        return $query->result();

    }

    public function get_entry($ip)
    {
        $this->db->select('a.*');

        $this->db->where('a.ip', $ip);

        $query = $this->db->get($this->_table . ' a');

        if ($query->num_rows() > 0) {
            return $query->row();
        }

        // throw new Exception('Ocorreu um problema, o IP não foi encontrado.');
    }

    public function remove($id)
    {
        try {

            if (!empty($id)) {
                if (is_array($id) && count($id) > 0) {
                    $this->db->where_in('id', $id);
                } else {
                    $this->db->where('id', $id);
                }

                return $this->db->delete($this->_table);
            }

            return false;
        } catch (Exception $e) {
            return false;
        }

    }

    public function bloquear($ip, $bloquear = true)
    {
        $this->db->where('ip', $ip);
        return $this->db->update('ip', array('block_ip' => $bloquear, 'permanently_block' => false, 'block_date' => date('Y-m-d H:i:s')));
    }

    public function bloquear_permanentemente($ip)
    {
        if (empty($ip)) {
            throw new Exception('Ip não encontrado');
        }

        $this->db->where('ip', $ip);
        return $this->db->update('ip', array('permanently_block' => true));
    }

    public function get_entries_by_ip($ip, $limit = NULL, $offset = NULL, $count = FALSE)
    {
        if (empty($ip)) {
            throw new Exception('É necessário informar um IP para sua busca');
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            $this->db->where('p.criado_em >=', format_date('Y-m-d 00:00:00', $data_ini));
        }

        if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->where('p.criado_em <=', format_date('Y-m-d 23:59:59', $data_fim));
        }

        if ($situacao = $this->get_state('filter.situacao')) {
            if ($situacao == 2) {
                $this->db->where('b.permanently_block', true);
            }

            if ($situacao == 1) {
                $this->db->where('b.block_ip', true)->where('b.permanently_block', false);
            }

            if ($situacao == 3) {
                $this->db->where('b.block_ip', false)->where('b.permanently_block', false);
            }
        }

        $this->db->join('acesso_ip p', 'p.ip = b.ip', 'inner');
        $this->db->where('b.ip', $ip);

        $this->db->order_by('p.criado_em', 'desc');

        $query = $this->db->get($this->_table.' b', $limit, $offset);

        return $query->result();
    }

    public function log($email = '')
    {
        $ipaddrress = get_ip();

        $this->db->insert($this->_table_log, [
            'email' => $email,
            'ip' => $ipaddrress,
            'criado_em' => date('Y-m-d H:i:s')
        ]);

        $ip = $this->get_entry($ipaddrress);

        // if (empty($ip)) {
        //     $this->db->insert($this->_table, array(
        //         'ip' => $ipaddrress,
        //         'block_ip' => false,
        //         'permanently_block' => false
        //     ));
        // }

        if (!empty($ip) && $this->can_block_ip($ip->ip) && !$ip->block_ip && !$ip->permanently_block) {
            $this->block_ip($ip->ip);

            redirect('/login');
        }
    }

    private function can_block_ip($ip)
    {
        if (!$this->config_auth_model->get_config_value('enable_block_ip')) {
            return false;
        }

        $incorretas_min = $this->config_auth_model->get_config_value('incorretas_min_ip');

        $this->load->model([
            'auth/ip_whitelist_model',
        ]);

        $ipInsideWhitelist = $this->ip_whitelist_model->get_entry_by_ip($ip);

        $time = strtotime(date("Y-m-d H:i:s")) - (60);

        $this->db->where('ip', $ip);
        $this->db->where('criado_em >=', date('Y-m-d H:i:s', $time));

        $query = $this->db->get($this->_table_log);

        if ($query->num_rows() >= $incorretas_min && empty($ipInsideWhitelist)) {
            return true;
        }

        return false;
    }

    private function block_ip($ip)
    {
        $this->db->where('ip', $ip);

        return $this->db->update($this->_table, array('block_ip' => true, 'permanently_block' => false, 'block_date' => date('Y-m-d H:i:s')));
    }

    public function check_ip($msgs)
    {
        $ip = $this->get_entry(get_ip());

        if (!empty($ip)) {
            if ($ip->block_ip) {
                throw new Exception($msgs['ip_bloqueado']);
            }

            if ($ip->permanently_block) {
                throw new Exception($msgs['ip_bloqueado_perm']);
            }
        }
    }

    public function liberar_ips_bloqueados()
    {
        $indeterminado = $this->config_auth_model->get_config_value('indeterminado_ip');

        if ($indeterminado) {
            return true;
        }

        $qtd_minutos = $this->config_auth_model->get_config_value('qtd_minutos_ip');

        $date = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s')) - ($qtd_minutos * 60));

        $this->db->where('block_ip', true);
        $this->db->where('block_date <=', $date);
        $this->db->where('permanently_block', false);

        return $this->db->update($this->_table, array('block_ip' => false));
    }
}
