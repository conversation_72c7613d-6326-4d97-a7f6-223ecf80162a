<?php

class Usuario_model extends MY_Model
{

    public $_table = 'usuario';

    const ID_GRUPO_MASTER = 1;

    public function __construct()
    {
        parent::__construct();

        $this->load->model('auth/acesso_ip_model');
    }

    public function set_logon($obj)
    {
        $this->load->model('log_usuario_model');

        $this->session->set_userdata('is_logged', true);
        $this->session->set_userdata('user_id', $obj->id_usuario);
        $this->session->set_userdata('user_nome', $obj->nome);
        $this->session->set_userdata('user_email', $obj->email);
        $this->session->set_userdata('user_grupo', $obj->id_grupo);
        $this->session->set_userdata('user_tipo', $obj->tipo);

        $log_data = array('id_usuario' => $obj->id_usuario, 'acesso_em' => date('Y-m-d H:i:s'));
        $this->log_usuario_model->save($log_data);
    }

    public function check_login($email, $senha)
    {
        // if (
        //     (strpos($email, '@dwtaxcom.br') !== false && $email != '<EMAIL>') ||
        //     (strpos($email, '@becomexpartner.com.br') !== false && $email != '<EMAIL>') ||
        //     (strpos($email, '@becomexsupplier.com.br') !== false && $email != '<EMAIL>') ||
        //     (strpos($email, '@becomexdigital.com.br') !== false && $email != '<EMAIL>') ||
        //     (strpos($email, '@quirius.com.br') !== false && $email != '<EMAIL>')
        // ) {
        //     return $this->import_user_ad($email, $senha);
        // }

        $usuario_pdc = $this->get_entry_by_email($email, false);

        if (!empty($usuario_pdc->id_usuario) && !$usuario_pdc->ativo) {
            return array('status' => false, 'message' => 'O cadastro do usuário não está mais ativo. Entre em contato com a equipe de suporte');
        }

        return $this->import_user_sdk($email, $senha);
    }

    private function authenticate_login($email, $senha)
    {
        $this->lang->load("default_password", sess_user_language());

        $query = $this->db->get_where($this->_table, array('email' => $email, 'ativo' => 1), 1);

        if ($query->num_rows()) {
            $row = $query->row();

            if ($this->password_verify($senha, $row->senha)) {
                /* Usuário existe e a senha está correta */
                return true;
            }

            throw new Exception($this->lang->line('alerta_invalidos'));
            /* Usuário existe e a senha está incorreta */
            // return false;
        }

        /* Usuário não existe ou está inativo */
        return null;
    }

    public function set_rel_usuario_seguidor($id_usuario, $grupos_seguidores)
    {
        // remove dados atuais do rel_usuario_empresa
        $this->db->where('id_usuario_seguidor', $id_usuario);
        $this->db->delete('rel_usuario_seguidor_grupo');

        if (is_array($grupos_seguidores) && !empty($grupos_seguidores)) {
            foreach ($grupos_seguidores as $grupo) {
                $rel_usuario_seguidor = array(
                    'id_usuario_seguidor' => $id_usuario,
                    'id_grupo' => $grupo
                );
                $this->db->insert('rel_usuario_seguidor_grupo', $rel_usuario_seguidor);
            }

            return true;
        }

        return false;
    }

    public function get_rel_usuarios_seguidores($id_usuario)
    {
        $this->db->where('id_usuario_seguidor', $id_usuario);
        $query = $this->db->get('rel_usuario_seguidor_grupo');

        if ($query->num_rows() === 0) return false;

        return $query->result();
    }

    public function check_user_client($client_id, $user_id)
    {
        if (!empty($client_id) && !empty($user_id)) {

            $this->db->join('rel_usuario_produto rup', 'u.id_usuario = rup.id_usuario', 'inner');
            $this->db->join('produto p', 'p.id_produto = rup.id_produto', 'inner');

            $this->db->where('u.id_usuario', $user_id);
            $this->db->where('p.client_id', $client_id);

            $query = $this->db->get($this->_table . ' u');
            $row = $query->row();

            if ($row->enable_oauth && $row->oauth_scope == "contract") {
                return true;
            }

            $this->db->where('client_id', $client_id);

            $query = $this->db->get('produto');
            $row = $query->row();

            if ($row->enable_oauth && ($row->oauth_scope == "none" || $row->oauth_scope == "global")) {
                return true;
            }
        }

        return false;
    }

    private function import_user_ad($email, $senha)
    {
        $this->load->library('ldap');
        $this->lang->load('model_messages', $this->session->userdata("language"));

        $ativo = true;

        try {
            /* Realiza autenticação e importação do AD */
            $this->ldap->authenticate($email, $senha);
        } catch (Exception $e) {
            $ativo = false;
        }

        try {
            /* E-mail existe, está ativo e a senha está correta no PDC */
            if ($this->authenticate_login($email, $senha) === true) {
                $usuario_pdc = $this->get_entry_by_email($email);

                if ($ativo === false) {
                    $id_usuario = $usuario_pdc->id_usuario;
                    $this->db->where('id_usuario', $id_usuario);
                    $this->db->update('usuario', array('ativo' => 0));

                    return array('status' => false, 'message' => 'O cadastro do usuário não está mais ativo. Entre em contato com a equipe de suporte');
                }

                /* Usuário pode logar
                *  E-mail existe, está ativo e a senha está correta no AD e no PDC
                */
                $logon = new StdClass();
                $logon->id_usuario = $usuario_pdc->id_usuario;
                $logon->nome       = $usuario_pdc->nome;
                $logon->email      = $usuario_pdc->email;
                $logon->id_grupo   = $usuario_pdc->id_grupo;
                $logon->tipo       = $usuario_pdc->tipo;

                $this->set_logon($logon);

                return array('status' => true, 'admin' => true);
            }

            if (!$this->check_exists_by_email($email)) {
                $this->acesso_ip_model->log($email);
            }

            return array('status' => false, 'message' => $this->lang->line("dados_inv_strong"));
        } catch (Exception $e) {
            return array('status' => false, 'message' => $this->lang->line("dados_inv_strong"));
        }
    }

    private function check_login_sdk($email, $senha)
    {
        $this->lang->load('model_messages', $this->session->userdata("language"));

        $secret = 's0ftd3sk@sfdk';
        $ts     = time();
        $hash   = md5($ts . $email . $secret);

        $params = array(
            'hash'  => $hash,
            'chave' => $ts,
            'login' => $email,
            // 'senha' => $this->sdk_criptografar_senha($senha)
        );

        $curl = curl_init();

        $sdk_url = 'https://becomex.soft4.com.br/_json/checar-login-bypass.php';

        curl_setopt_array($curl, array(
            CURLOPT_URL => $sdk_url . '?' . http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET"
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new Exception($err);
        }

        $response = json_decode($response);

        if (empty($response->status)) {
            if ($response->mensagem == 'Hash incorreto.') {
                $this->acesso_ip_model->log($email);
                throw new Exception($this->lang->line("dados_inc"));
            }

            throw new Exception($response->mensagem);
        }
    }

    private function get_usuario_sdk($params = NULL)
    {
        if (empty($params)) {
            throw new Exception('Nenhum parâmetro não informado');
        }

        $curl = curl_init();

        // Precisei alterar a pedido do Rafael da Soft4
        $sdk_url = 'https://becomex.soft4.com.br/api/api.php/usuario';

        curl_setopt_array($curl, array(
            CURLOPT_URL => $sdk_url . '?' . http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Accept: */*",
                "Cache-Control: no-cache",
                "cache-control: no-cache",
                "hash_api: 3LE2SM5OaBPVyc4DTeRF",
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new Exception($err);
        }

        $response = json_decode($response);

        return $response->objeto;
    }

    private function update_usuario_sdk($email, $senha)
    {
        $this->lang->load('model_messages', $this->session->userdata("language"));

        $usuario = $this->get_entry_by_email($email);

        $this->check_login_sdk($email, $senha);

        $sdk_response = $this->get_usuario_sdk(array(
            'login' => $email
        ));

        if (empty($sdk_response)) {
            throw new Exception($this->lang->line("dados_inc"));
        }

        /* Usuário existe e a senha está incorreta */
        if ($usuario != null && !$this->password_verify($senha, $usuario->senha)) {
            throw new Exception($this->lang->line("senha_inc"));
        }

        $this->db->update(
            $this->_table,
            array('senha'      => $this->password_hash($senha)),
            array('id_usuario' => $usuario->id_usuario)
        );

        return $this->get_entry_by_email($email);
    }

    private function create_user_sdk($email, $senha)
    {
        $this->lang->load('model_messages', $this->session->userdata("language"));

        $this->load->model('empresa_model');

        $this->check_login_sdk($email, $senha);
        $sdk_response = $this->get_usuario_sdk(array(
            'login' => $email
        ));

        if (empty($sdk_response)) {
            throw new Exception($this->lang->line("dados_incs"));
        }

        $this->db = $this->load->database('default', true);

        if (empty($sdk_response->filial->cnpj)) {
            throw new Exception($this->lang->line("filial").' <strong>' . ($sdk_response->nome ? $sdk_response->nome : NULL) . '</strong>.');
        }

        $cnpj_filial = preg_replace('/[^0-9]/', '', $sdk_response->filial->cnpj);
        $empresa = $this->empresa_model->get_entry_by_cnpj($cnpj_filial, true);

        if (empty($empresa)) {
            throw new Exception($this->lang->line("empresa").' (<strong>' . $sdk_response->filial->cnpj . '</strong>).');
        }

        $id_grupo = 0;
        $id_perfil = 13; // Prospect
        if ($empresa->tipo_empresa == 'cliente') {
            $id_grupo = $empresa->id_grupo;

            // if (
            //     (strpos($email, '@dwtaxcom.br') !== false && $email != '<EMAIL>') ||
            //     (strpos($email, '@becomexpartner.com.br') !== false && $email != '<EMAIL>') ||
            //     (strpos($email, '@becomexsupplier.com.br') !== false && $email != '<EMAIL>') ||
            //     (strpos($email, '@becomexdigital.com.br') !== false && $email != '<EMAIL>') ||
            //     (strpos($email, '@quirius.com.br') !== false && $email != '<EMAIL>')
            // ) {
            //     $id_perfil = 10; // Becomex Colaborador
            // }

            if (empty($id_grupo))
                throw new Exception('A empresa <strong>' . $empresa->razao_social . '</strong> não possui nenhum <strong>grupo</strong> cadastrado no Portal.');
        }

        if ($this->get_entry_by_email($sdk_response->email)) {
            throw new Exception('Encontramos uma conta já ativa para este usuário. Tente acessar informando o email.');
        }

        $dbdata = array(
            'nome'      => $sdk_response->nome,
            'email'     => $sdk_response->email,
            'senha'     => $this->password_hash($senha),
            'id_grupo'  => $id_grupo,
            'id_perfil' => $id_perfil,
            'tipo'      => $empresa->tipo_empresa
        );


        $id_usuario = $this->usuario_model->save($dbdata);

        if (empty($id_usuario))
            throw new Exception('Não foi possível sincronizar o usuário. Tente novamente');

        $dbdata_rel_empresa = array(
            'id_empresa' => $empresa->id_empresa,
            'id_usuario' => $id_usuario
        );

        $this->db->insert('rel_usuario_empresa', $dbdata_rel_empresa);

        // Caso for prospect, vincula ao CPD
        if ($empresa->tipo_empresa == 'prospect') {
            $dbdata_rel_produto = array(
                'id_produto' => 20, // Id produto CPD
                'id_usuario' => $id_usuario
            );
            $this->db->insert('rel_usuario_produto', $dbdata_rel_produto);
        }

        return $this->get_entry_by_email($email);
    }

    private function do_login_sdk($usuario = NULL, $email = '')
    {
        $this->lang->load('model_messages', $this->session->userdata("language"));

        if (empty($usuario)) {
            $this->acesso_ip_model->log($email);
            throw new Exception($this->lang->line("dados_inv"));
        }

        $logon = new StdClass();
        $logon->id_usuario = $usuario->id_usuario;
        $logon->nome       = $usuario->nome;
        $logon->email      = $usuario->email;
        $logon->id_grupo   = $usuario->id_grupo;
        $logon->tipo       = $usuario->tipo;

        $this->set_logon($logon);
    }

    private function import_user_sdk($email = NULL, $senha = NULL)
    {

        try {
            if (empty($email)) {
                throw new Exception($this->lang->line("email"));
            }

            if (empty($senha)) {
                throw new Exception($this->lang->ling("senha"));
            }

            $verified = $this->authenticate_login($email, $senha);

            if (empty($verified)) {
                throw new Exception($this->lang->line("alerta_invalidos"));
            }

            /**
             * Usuário existe no PDC
             * E-mail e senha corretos. Pode realizar o login direto
             * @var mixed
             */
            if ($verified === true) {
                $usuario = $this->get_entry_by_email($email);
            }

            /**
             * Usuário existe no PDC
             * Senha incorreta. Se existir no SDK, atualiza o PDC
             * @var mixed
             */
            if ($verified === false) {
                $usuario = $this->update_usuario_sdk($email, $senha);
            }

            /**
             * Usuário não existe no PDC
             * Se existir no SDK, importa os dados do usuário
             * @var mixed
             */
            if ($verified === null) {
                $usuario = $this->create_user_sdk($email, $senha);
            }

            // Reliza o login com as credênciais informadas
            $this->do_login_sdk($usuario, $email);
        } catch (Exception $e) {
            return array(
                'status' => false,
                'message' => $e->getMessage()
            );
        }

        return array('status' => true, 'cliente' => true);
    }

    public function password_hash($passwd, $sha1 = true)
    {
        $passwordstr = ($sha1 == true ? sha1($passwd) : $passwd);
        return password_hash($passwordstr, PASSWORD_BCRYPT);
    }

    public function password_verify($passwd, $hash)
    {
        return password_verify(sha1($passwd), $hash);
    }

    public function get_total_entries()
    {
        // $this->db->select('u.*, p.descricao AS perfil, g.nome as grupo, GROUP_CONCAT(e.razao_social SEPARATOR ", ") as empresas', false);
        $this->db->join('perfil p', 'u.id_perfil = p.id_perfil', 'inner');
        // $this->db->join('rel_usuario_empresa rel', 'rel.id_usuario = u.id_usuario', 'left');
        // $this->db->join('empresas e', 'e.id_empresa = rel.id_empresa', 'left');

        if ($perm_slug = $this->get_state('filter.permissao_slug')) {
            $this->db->join('perfil_permissao pp', 'pp.id_perfil = p.id_perfil', 'inner');
            $this->db->join('permissao perm', 'perm.id_permissao = pp.id_permissao', 'inner');
            $this->db->where('perm.slug', $perm_slug);
        }

        if ($id_grupo = $this->get_state('filter.id_grupo')) {
            $this->db->where_in('u.id_grupo', $id_grupo);
        }

        if ($id_produto = $this->get_state('filter.id_produto')) {
            $this->db->join('contrato c', 'c.id_grupo = u.id_grupo', 'left');
            $this->db->where_in('c.id_produto', $id_produto);
            $current_date = date('Y-m-d H:i:s');
            $this->db->where("c.final_vigencia >= '{$current_date}'", NULL);
            $this->db->where("(u.id_usuario IN (SELECT usuario.id_usuario FROM rel_usuario_empresa RIGHT JOIN usuario ON (usuario.id_usuario = rel_usuario_empresa.id_usuario) WHERE rel_usuario_empresa.id_usuario IS NOT NULL) OR u.id_grupo IN (SELECT id_grupo FROM rel_grupo_empresa WHERE id_grupo IN (SELECT rel_e.id_grupo FROM rel_grupo_empresa rel_e LEFT JOIN rel_usuario_empresa rel_g ON (rel_g.id_empresa = rel_e.id_empresa) WHERE rel_g.id_empresa IS NULL GROUP BY rel_e.id_grupo)))", NULL);
        }

        if ($id_perfil = $this->get_state('filter.id_perfil')) {
            $this->db->where('u.id_perfil', $id_perfil);
        }

        if (($status = $this->get_state('filter.status')) != NULL) {
            $this->db->where('u.ativo', $status);
        }

        if ($busca = $this->get_state('filter.nome_usuario')) {
            $this->db->where("(u.nome LIKE '%$busca%' OR u.email LIKE '%$busca%')", NULL, true);
        }

        $this->db->group_by('u.id_usuario');

        $this->db->join('grupo g', 'u.id_grupo = g.id_grupo', 'left');

        // $this->db->order_by('trim(u.nome), ativo');

        $query = $this->db->get($this->_table . ' u');

        return $query->num_rows();
    }

    public function get_entries($limit = NULL, $offset = NULL)
    {
        $this->db->select('u.*, p.descricao AS perfil, g.nome as grupo, GROUP_CONCAT(e.razao_social SEPARATOR ", ") as empresas', false);
        $this->db->join('perfil p', 'u.id_perfil = p.id_perfil', 'inner');
        $this->db->join('rel_usuario_empresa rel', 'rel.id_usuario = u.id_usuario', 'left');
        $this->db->join('empresas e', 'e.id_empresa = rel.id_empresa', 'left');
        // $this->db->where('e.tipo', 'cliente');
        // $this->db->join('empresas prospect', 'prospect.id_empresa = rel.id_empresa', 'left');
        // $this->db->where('prospect.tipo', 'prospect');

        if ($perm_slug = $this->get_state('filter.permissao_slug')) {
            $this->db->join('perfil_permissao pp', 'pp.id_perfil = p.id_perfil', 'inner');
            $this->db->join('permissao perm', 'perm.id_permissao = pp.id_permissao', 'inner');
            $this->db->where('perm.slug', $perm_slug);
        }

        if ($id_grupo = $this->get_state('filter.id_grupo')) {
            $this->db->where_in('u.id_grupo', $id_grupo);
        }

        if ($id_produto = $this->get_state('filter.id_produto')) {
            $this->db->join('contrato c', 'c.id_grupo = u.id_grupo OR c.cnpj = e.cnpj', 'left');
            $this->db->where_in('c.id_produto', $id_produto);
            $current_date = date('Y-m-d H:i:s');
            $this->db->where("c.final_vigencia >= '{$current_date}'", NULL);
            $this->db->where("(u.id_usuario IN (SELECT usuario.id_usuario FROM rel_usuario_empresa RIGHT JOIN usuario ON (usuario.id_usuario = rel_usuario_empresa.id_usuario) WHERE rel_usuario_empresa.id_usuario IS NOT NULL) OR u.id_grupo IN (SELECT id_grupo FROM rel_grupo_empresa WHERE id_grupo IN (SELECT rel_e.id_grupo FROM rel_grupo_empresa rel_e LEFT JOIN rel_usuario_empresa rel_g ON (rel_g.id_empresa = rel_e.id_empresa) WHERE rel_g.id_empresa IS NULL GROUP BY rel_e.id_grupo)))", NULL);
        }

        if ($id_perfil = $this->get_state('filter.id_perfil')) {
            $this->db->where('u.id_perfil', $id_perfil);
        }

        if (($status = $this->get_state('filter.status')) != NULL) {
            $this->db->where('u.ativo', $status);
        }

        if ($busca = $this->get_state('filter.nome_usuario')) {
            $this->db->where("(u.nome LIKE '%{$busca}%' OR u.email LIKE '%{$busca}%')", NULL, true);
        }

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where_in('rel.id_empresa', $id_empresa);
        }

        $this->db->group_by('u.id_usuario');

        // "LEFT" ao inves de "INNER" para admin poder regularizar
        // contas com "u.id_grupo=grupo com id removido"
        $this->db->join('grupo g', 'u.id_grupo = g.id_grupo', 'left');
        // $this->db->order_by('u.id_usuario', 'DESC');
        $this->db->order_by('trim(u.nome), ativo');

        $query = $this->db->get($this->_table . ' u', $limit, $offset);

        return $query->result();
    }

    public function get_usuarios_prospects_sem_contrato()
    {
        $filter_prospect = $this->get_state('filter.id_prospect');
        $filter_produto  = $this->get_state('filter.id_produto');

        $this->db->select('u.*');

        $this->db->join('rel_usuario_produto rel_up', 'rel_up.id_usuario = u.id_usuario', 'inner');
        $this->db->join('rel_usuario_empresa rel_ue', 'rel_ue.id_usuario = u.id_usuario', 'inner');
        $this->db->join('produto p', 'p.id_produto = rel_up.id_produto', 'inner');

        if (!empty($filter_produto)) {
            if (is_array($filter_produto)) {
                $this->db->where_in('p.id_produto', $filter_produto);
            } else {
                $this->db->where('p.id_produto', $filter_produto);
            }
        }

        if (!empty($filter_prospect)) {
            if (is_array($filter_prospect)) {
                $this->db->where_in('p.id_produto', $filter_prospect);
            } else {
                $this->db->where('rel_ue.id_empresa', $filter_prospect);
            }
        }

        $this->db->group_by('u.id_usuario');
        $this->db->order_by('u.nome', 'ASC');

        $query = $this->db->get('usuario u');

        return $query->result();
    }

    public function get_entry($id_usuario)
    {
        // Faz a consulta pra pegar o tipo de usuario
        $this->db->where('id_usuario', $id_usuario);
        $query = $this->db->get($this->_table . ' u');
        $row = $query->row();

        if ($row->tipo != 'prospect') {

            $this->db->select('u.*, g.nome as grupo, "" as empresas', false);
            $this->db->join('grupo g', 'u.id_grupo = g.id_grupo', 'left');
        } else {

            $this->db->select('u.*, "" as grupo, GROUP_CONCAT(e.razao_social SEPARATOR ", ") as empresas', false);

            $this->db->join('rel_usuario_empresa rel', 'rel.id_usuario = u.id_usuario', 'left');
            $this->db->join('empresas e', 'rel.id_empresa = e.id_empresa', 'left');
            $this->db->where('e.tipo', 'prospect');
        }

        $this->db->where('u.id_usuario', $id_usuario);
        $query = $this->db->get($this->_table . ' u');

        if ($query->num_rows() > 0) {
            return $query->row();
        }

        throw new Exception('Código de usuário inexistente.');
    }

    public function get_entry_by_email($email = null, $check_ativo = true)
    {
        if (!$email) return false;

        if ($check_ativo) {
            $query = $this->db->get_where($this->_table, array('email' => $email, 'ativo' => 1), 1);
        } else {
            $query = $this->db->get_where($this->_table, array('email' => $email));
        }

        return $query->row();
    }

    public function check_exists_by_email($email)
    {
        $usuario = $this->get_entry_by_email($email);

        if (!empty($usuario) && $usuario->email) {
            return true;
        }

        return false;
    }

    public function check_hash($email = null, $hash = null)
    {
        if (!$email || !$hash) return false;

        $query = $this->db->get_where($this->_table, array(
            'email' => $email,
            'hash' => $hash
        ), 1);
        return $query->row();
    }

    public function get_usuarios_by_grupo($id_grupo = 0)
    {

        $receber_notificacoes = $this->get_state('filter.receber_notificacoes');

        if ($receber_notificacoes == true) {
            $this->db->where('receber_notificacoes', 1);
        }

        $this->db->where('id_grupo', $id_grupo);
        $this->db->order_by('nome', 'ASC');
        $query = $this->db->get($this->_table);

        return $query->result();
    }

    public function get_usuarios_by_cnpj($cnpj = 0)
    {

        $add_where = '';
        $receber_notificacoes = $this->get_state('filter.receber_notificacoes');

        if ($receber_notificacoes == true) {
            $add_where .= 'AND receber_notificacoes = 1';
        }

        $sub_sql_cnpj_empresa = "SELECT id_empresa FROM empresas WHERE cnpj = '{$cnpj}' and tipo = 'cliente'";
        $sub_sql_rel_usuario_empresa = "SELECT id_usuario FROM rel_usuario_empresa WHERE id_empresa = ({$sub_sql_cnpj_empresa})";
        $sql_usuarios = "SELECT * FROM usuario WHERE id_usuario IN ({$sub_sql_rel_usuario_empresa}) ";
        $sql_usuarios .= $add_where . " ";
        $sql_usuarios .= "ORDER BY nome ASC";

        $query = $this->db->query($sql_usuarios);

        return $query->result();
    }

    public function get_user_company($id_usuario)
    {
        $this->db->select('id_empresa');
        $this->db->where('u.id_usuario', $id_usuario);
        $this->db->join('rel_usuario_empresa rel', 'rel.id_usuario=u.id_usuario', 'inner');
        $query = $this->db->get($this->_table . ' u');
        if ($query->num_rows() > 0) {
            $row = $query->row();
            return $row->id_empresa;
        }

        return NULL;
    }

    public function get_user_companies_by_grupo($id_usuario)
    {
        $usuario = $this->get_entry($id_usuario);

        $this->db->join('rel_grupo_empresa rge', 'rge.id_empresa = e.id_empresa');

        $this->db->where('rge.id_grupo', $usuario->id_grupo);

        if ($usuario->tipo != 'prospect') {
            $this->db->where('e.tipo', 'cliente');
            $query = $this->db->get('empresas e');
        } else {
            $this->db->where('e.tipo', 'prospect');
            $query = $this->db->get('empresas e');
        }

        return $query->result();
    }

    public function get_user_companies($id_usuario, $return_id = false)
    {
        $usuario = $this->get_entry($id_usuario);

        $this->db->select('e.id_empresa, e.cnpj');
        $this->db->where('u.id_usuario', $id_usuario);
        $this->db->join('rel_usuario_empresa rel', 'rel.id_usuario=u.id_usuario', 'inner');

        if ($usuario->tipo != 'prospect') {
            $this->db->join('empresas e', 'rel.id_empresa = e.id_empresa', 'inner');
            $this->db->where('e.tipo', 'cliente');
        } else {
            $this->db->where('e.tipo', 'prospect');
            $this->db->join('empresas e', 'rel.id_empresa = e.id_empresa', 'inner');
        }

        $query = $this->db->get($this->_table . ' u');

        $arr = array();

        if ($query->num_rows() > 0) {
            if ($return_id === true) {
                foreach ($query->result() as $row) {
                    $arr[] = $row->id_empresa;
                }
            } else {
                foreach ($query->result() as $row) {
                    $arr[] = $row->cnpj;
                }
            }
        }

        return $arr;
    }

    public function remove($id_list)
    {

        try {

            if (is_array($id_list) && count($id_list) > 0) {

                // remove dados do rel_usuario_empresa
                $this->db->where_in('id_usuario', $id_list);
                $this->db->delete('rel_usuario_empresa');

                // remove o usuario em si
                $this->db->where_in('id_usuario', $id_list);

                return $this->db->delete($this->_table);
            }
        } catch (Exception $e) {

            return false;
        }
    }

    public function set_rel_usuario_empresa($id_usuario, $empresas)
    {

        if (!is_array($empresas)) {
            return false;
        }

        try {

            $insert_data = array();

            // remove dados atuais do rel_usuario_empresa
            $this->db->where('id_usuario', $id_usuario);
            $this->db->delete('rel_usuario_empresa');

            // e adiciona os novos dados
            foreach ($empresas as $id_empresa) {

                $insert_data = array(
                    'id_usuario' => $id_usuario,
                    'id_empresa' => $id_empresa
                );

                $this->db->insert('rel_usuario_empresa', $insert_data);
            }

            return true;
        } catch (Exception $e) {

            return false;
        }
    }

    public function get_photo_profile($id)
    {
        $path = base_url('assets/uploads/perfil') . '/';

        $image = glob(FCPATH . '/assets/uploads/perfil/' . $id . '.*', GLOB_BRACE);

        if (empty($image)) {
            $image = base_url('assets/frontend/imgs/profile-default.png');
        } else {
            $image = $path . basename(array_shift($image));
        }

        return $image;
    }

    public function get_entries_notificacao_ativa($id_produto)
    {
        $this->load->model('contrato_model');

        // Resgata todos os contratos vigentes.
        $current_date = date('Y-m-d');
        $this->db->where('c.id_produto', $id_produto);
        $this->db->where("c.final_vigencia >= '{$current_date}'", NULL, true);
        $query = $this->db->get('contrato c');

        $usuarios_entry = array();
        $contratos_entry = $query->result();

        foreach ($contratos_entry as $contrato) {

            $this->set_state('filter.receber_notificacoes', true);
            if (!empty($contrato->id_grupo)) {
                $usuarios_obj = $this->get_usuarios_by_grupo($contrato->id_grupo);
            } else {
                $usuarios_obj = $this->get_usuarios_by_cnpj($contrato->cnpj);
            }

            if (!empty($usuarios_obj)) {

                foreach ($usuarios_obj as $usuarios) {
                    $usuarios_entry[$usuarios->email] = (object) array(
                        'id_usuario' => $usuarios->id_usuario,
                        'nome' => $usuarios->nome,
                        'email' => $usuarios->email,
                    );
                }
            }
        }

        return $usuarios_entry;
    }

    public function clear_rel_produto($id_usuario)
    {
        return $this->db->delete('rel_usuario_produto', array('id_usuario' => $id_usuario));
    }

    public function save_rel_produto($id_usuario, $id_produto)
    {
        $this->db->delete('rel_usuario_produto', array(
            'id_usuario' => $id_usuario,
            'id_produto' => $id_produto
        ));

        $dbdata = array('id_usuario' => $id_usuario, 'id_produto' => $id_produto);

        return $this->db->insert('rel_usuario_produto', $dbdata);
    }

    public function get_rel_usuario_produtos($id_usuario, $ids_as_array = false)
    {
        if ($ids_as_array === true) {
            $this->db->select('id_produto');
        }

        $this->db->where('id_usuario', $id_usuario);
        $query = $this->db->get('rel_usuario_produto');

        if ($ids_as_array === true) {
            $arr_result = array();

            $rs = $query->result();
            foreach ($rs as $row) {
                $arr_result[] = $row->id_produto;
            }

            return $arr_result;
        } else {
            return $query->result();
        }
    }

    public function check_has_permission($id_usuario, $id_produto)
    {
        $this->db->select('u.id_usuario');
        $this->db->join('rel_usuario_produto rel', 'rel.id_usuario = u.id_usuario', 'left');
        $this->db->where("(u.id_usuario = '{$id_usuario}' AND rel.id_produto = '{$id_produto}')", NULL);
        $this->db->or_where("(SELECT id_usuario FROM rel_usuario_produto WHERE id_usuario = '{$id_usuario}' LIMIT 1) IS NULL", NULL);

        $query = $this->db->get('usuario u', 1);

        if ($query->num_rows() > 0) {
            return true;
        } else {
            return false;
        }
    }

    private function sdk_criptografar_senha($plaintext)
    {
        $key = base64_decode('FFhmo2EweG7PHR9UvfQVse/R+Yx/KLLpdAh2JpXDlmA=');
        $ivlen = openssl_cipher_iv_length($cipher = "AES-128-CBC");
        $iv = openssl_random_pseudo_bytes($ivlen);
        $ciphertext_raw = openssl_encrypt($plaintext, $cipher, $key, $options = OPENSSL_RAW_DATA, $iv);
        $hmac = hash_hmac('sha256', $ciphertext_raw, $key, $as_binary = true);
        $ciphertext = base64_encode($iv . $hmac . $ciphertext_raw);

        return urlencode($ciphertext);
    }

    public function get_usuarios_prospects_sem_grupo()
    {
        $this->db->select('u.*');

        $this->db->where('u.id_grupo', '');
        $this->db->where('rel_emp.id_empresa != 0');

        $this->db->group_by('u.id_usuario');

        $this->db->order_by('u.nome', 'ASC');

        $this->db->join('rel_usuario_empresa rel_emp', 'rel_emp.id_usuario = u.id_usuario', 'left');

        $query = $this->db->get($this->_table . ' u');

        return $query->result();
    }

    public function get_entries_becomex_sent_file()
    {
        $this->db->select('u.*');

        $this->db->where('u.id_grupo', 117);

        $this->db->group_by('u.id_usuario');

        $this->db->order_by('u.nome', 'ASC');

        $this->db->join('arquivo_cliente a', 'a.id_usuario = u.id_usuario', 'inner');

        $query = $this->db->get($this->_table . ' u');

        return $query->result();
    }

    public function isPwdExpired($usuario)
    {
        $expirationPwd = new \DateTime($usuario->expiration_time);
        if ($expirationPwd <= new \DateTime()) {
            return true;
        }
    }

    public function get_rel_usuarios_seguidores_grupo($id_usuario, $id_grupo)
    {
        if ($id_grupo != null && $id_usuario != null) {
            $this->db->where('id_grupo', $id_grupo);
            $this->db->where('id_usuario_seguidor', $id_usuario);
            $query = $this->db->get('rel_usuario_seguidor_grupo');
            if ($query && count($query->result()) > 0) {
                return $query->result();
            }
        }

        return array();
    }

    public function get_rel_usuarios_grupo($id_grupo)
    {
        if ($id_grupo != null) {
            $this->db->select('id_usuario_seguidor');
            $this->db->where('id_grupo', $id_grupo);
            $query = $this->db->get('rel_usuario_seguidor_grupo');
            if ($query && count($query->result()) > 0) {
                return $query->result();
            }
        }

        return array();
    }

    public function isValidFa()
    {
        return (base64_decode(sess_user_fa()) != sess_user_fa_code() || !sess_user_fa());
    }


    //Regra de dois fatores de autenticação 2FA
    public function check_fa($usuario)
    {
        if (is_logged() && $this->router->class != 'login' && !config_item('force_oauth2_login')) {
            $this->load->model(array('auth/config_auth_model', 'two_factor_auth_model'));

            if (
                $this->two_factor_auth_model->check_send_email_code($usuario->last_access, $usuario->id_grupo) &&
                $this->isValidFa()
            ) {
                redirect('/login/login_check_auth');
            } else if ($usuario && $this->isPwdExpired($usuario)) {
                redirect('/login/alterar_senha');
            }
        }
    }

    public function habilitar($id_usuario, $habilitar = true)
    {
        $this->db->where('id_usuario', $id_usuario);
        return $this->db->update('usuario', array('ativo' => $habilitar));
    }

    public function bloquear($id_usuario, $bloquear = true)
    {
        $this->db->where('id_usuario', $id_usuario);

        return $this->db->update('usuario', array('block' => $bloquear, 'permanently_block' => false, 'block_date' => date('Y-m-d H:i:s')));
    }

    public function bloquear_permanentemente($id_usuario)
    {
        if (empty($id_usuario)) {
            throw new Exception('Usuário não encontrado');
        }

        if ($id_usuario == sess_user_id()) {
            throw new Exception('Não é possível bloquear seu próprio usuário');
        }

        $this->db->where('id_usuario', $id_usuario);
        return $this->db->update('usuario', array('permanently_block' => true));
    }

    public function check_user_can_login($usuario) {
        if (empty($usuario)) {
            throw new Exception('Usuário não encontrado');
        }

        if ($usuario->block) {
            throw new Exception('Usuário bloqueado');
        }

        if ($usuario->permanently_block) {
            throw new Exception('Usuário bloqueado permanentemente');
        }
    }

    public function get_permissoes_by_usuario($id_usuario) {
        // Faz a consulta pra pegar o tipo de usuario
        $this->db->select('p.*');
        $this->db->where('id_usuario', $id_usuario);
        $this->db->join('perfil_permissao pp', 'pp.id_perfil = u.id_perfil');
        $this->db->join('permissao p', 'p.id_permissao = pp.id_permissao');

        $query = $this->db->get($this->_table . ' u');

        return $query->result();
    }

    public function get_perfil_by_usuario($id_usuario) {
        // Faz a consulta pra pegar o tipo de usuario
        $this->db->select('p.*');
        $this->db->where('id_usuario', $id_usuario);
        $this->db->join('perfil p', 'p.id_perfil = u.id_perfil');

        $query = $this->db->get($this->_table . ' u');

        return $query->result();
    }

    public function get_usuarios_ad_ativos()
    {
        $this->db->where('ativo', '1');
        $this->db->like('email', '@dwtaxcom.br');
        $query = $this->db->get($this->_table);

        return $query->result();
    }

    public function get_destinatarios_email()
    {
        $this->db->where('id_grupo', self::ID_GRUPO_MASTER);
        $this->db->where('ativo', 1);
        $this->db->like('email', '@dwtaxcom.br');
        $this->db->order_by('nome', 'ASC');

        $query = $this->db->get($this->_table);

        return $query->result();
    }

    public function get_destinatarios_by_email($emails)
    {
        if (empty($emails)) {
            return array();
        }

        $this->db->where('id_grupo', self::ID_GRUPO_MASTER);
        $this->db->where('ativo', 1);
        $this->db->where_in('email', $emails);

        $query = $this->db->get($this->_table);

        return array_map(function($usuario) {
            return $usuario->id_usuario;
        }, $query->result());
    }

    public function get_autores()
    {
        $this->db->select('nome');
        $query = $this->db->get('usuario');
        return $query->result();
    }

    public function is_valid_becomex_user($email) {
       // $this->db->where('id_grupo', self::ID_GRUPO_MASTER);
        $this->db->where('ativo', 1);
        $this->db->where('email', $email);

        $query = $this->db->get($this->_table);

        return $query->num_rows() > 0 ? true : false;
    }

    public function check_api_integracao_login($email, $senha)
    {
        $query = $this->db->get_where($this->_table, array('email' => $email), 1);

        if ($query->num_rows()) {
            $row = $query->row();

            if (!$this->password_verify($senha, $row->senha)) {
                return false;
            }

            return $row->id_usuario;
        }
        return false;
    }

    public function update_language($id, $language)
    {
        $this->db->where("id_usuario", $id);

        return $this->db->update('usuario', array('language' => $language));
    }

    // Função para verificar se o email do usuário, recebido por parâmetro, pertecence ao dominio da Becomex, como @becomex, @becomexpartner, @becomexsupplier, @becomexdigital e @quirius
    public function check_email_becomex($email)
    {
        $email = strtolower($email);
        $email = explode('@', $email);
        $email = $email[1];

        $dominios = array('becomex.com.br', 'becomexpartner.com.br', 'becomexsupplier.com.br', 'becomexdigital.com.br', 'quirius.com.br');

        if (in_array($email, $dominios)) {
            return true;
        }

        return false;
    }

    public function insert_rel_usuario_empresa_becomex($id_usuario)
    {
        if (!$id_usuario || empty($id_usuario)) {
            return false;
        }

        $id_empresa_becomex = 26646; // Becomex Consulting

        $this->db->insert('rel_usuario_empresa',
                            array(
                                'id_usuario' => $id_usuario,
                                'id_empresa' => $id_empresa_becomex));

        return true;
    }

    public function insert_user_becomex($name, $email)
    {
        if (!$name || empty($name) || !$email || empty($email)) {
            return false;
        }

        $dbdata = array(
            'nome' => $name,
            'email' => $email,
            'senha' => $this->password_hash($email),
            'ativo' => 1,
            'id_perfil' => 10,
            'id_grupo' => 117,
            'tipo' => 'colaborador',
        );

        $this->db->insert('usuario', $dbdata);

        $id_usuario = $this->db->insert_id();

        if (!$id_usuario || empty($id_usuario)) {
            return false;
        }

        return $id_usuario;
    }

}
